package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 普通节点操作命令
 * 实现普通节点的新增、删除、修改等操作
 */
@Slf4j
public class NodeOperations {
    
    private final NodeBatchExecutor nodeBatchExecutor;
    private final AutoWireStrategy autoWireStrategy;
    
    public NodeOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.autoWireStrategy = autoWireStrategy;
    }
    

    /**
     * 插入节点
     * 根据设计方案：在nextId=99对应的节点之后进行插入操作，自动把插入节点的nextId变为99
     * @param afterNodeId 在此节点之后插入
     * @param newNode 新节点规格
     * @return 创建的节点
     */
    public BaseNodeCanvas insertNode(String afterNodeId, BaseNodeCanvas newNode) {
        if (nodeBatchExecutor == null || afterNodeId == null || newNode == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas afterNode = workingCopy.getFlowNodeMap().get(afterNodeId);
        if (afterNode == null) {
            throw new IllegalArgumentException("找不到插入点节点: " + afterNodeId);
        }

        if (!(afterNode instanceof IRoutable routableAfterNode)) {
            throw new IllegalArgumentException("插入点节点必须是可路由的");
        }

        String originalNextId = routableAfterNode.getNextId();

        nodeBatchExecutor.createNode(newNode);

        // 使用AutoWireStrategy进行连接
        List<BaseNodeCanvas> prevNodes = Arrays.asList(afterNode);

        if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
            // 根据设计方案：在nextId=99对应的节点之后进行插入操作，自动把插入节点的nextId变为99
            // 原EndOwner节点不再指向99，新插入的节点成为EndOwner
            autoWireStrategy.spliceBetween(workingCopy, prevNodes, newNode, newNode, null);

            // 新节点连接到结束，成为新的EndOwner
            autoWireStrategy.connectToEnd(workingCopy, newNode);

            log.debug("在EndOwner节点 {} 后插入新节点 {}，新节点成为EndOwner", afterNodeId, newNode.getId());
        } else {
            // 普通插入
            autoWireStrategy.spliceBetween(workingCopy, prevNodes, newNode, newNode, originalNextId);

            // 更新下一个节点的前驱
            if (originalNextId != null && !originalNextId.trim().isEmpty()) {
                BaseNodeCanvas nextNode = workingCopy.getFlowNodeMap().get(originalNextId);
                if (nextNode instanceof IRoutable routableNext) {
                    routableNext.setPrveId(newNode.getId());
                    nodeBatchExecutor.updateNode(originalNextId, nextNode);
                }
            }

            log.debug("在节点 {} 后插入新节点 {}，连接到 {}", afterNodeId, newNode.getId(), originalNextId);
        }

        // 设置新节点的路由信息
        if (newNode instanceof IRoutable routableNew) {
            routableNew.setPrveId(afterNodeId);
        }

        // 更新afterNode
        nodeBatchExecutor.updateNode(afterNodeId, afterNode);

        log.debug("成功在节点 {} 后插入新节点 {}", afterNodeId, newNode.getId());

        return newNode;
    }
    
    /**
     * 删除节点
     * 根据设计方案：如果删除的是EndOwner，需要重新选择EndOwner并修复所有扇入
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(String nodeId) {
        if (nodeBatchExecutor == null || nodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas nodeToDelete = workingCopy.getFlowNodeMap().get(nodeId);
        if (nodeToDelete == null) {
            throw new IllegalArgumentException("找不到要删除的节点: " + nodeId);
        }

        if (!(nodeToDelete instanceof IRoutable routableNode)) {
            throw new IllegalArgumentException("只能删除可路由的节点");
        }

        log.info("开始删除节点: {}", nodeId);

        // 查找前驱节点
        List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(workingCopy, nodeId);
        String nextId = routableNode.getNextId();

        // 查找所有指向被删除节点的节点（不仅仅是前驱）
        List<BaseNodeCanvas> pointingNodes = findAllNodesPointingTo(workingCopy, nodeId);

        log.debug("删除节点 {}，找到 {} 个前驱节点，{} 个指向节点",
                 nodeId, prevNodes.size(), pointingNodes.size());
        for (BaseNodeCanvas pointing : pointingNodes) {
            log.debug("指向节点: {}", pointing.getId());
        }

        // 检查是否删除的是EndOwner
        boolean isEndOwner = EndOwnerManager.END_OWNER_ID.equals(nextId);

        log.debug("删除节点 {} 的nextId: {}, 是否为EndOwner: {}", nodeId, nextId, isEndOwner);

        if (isEndOwner) {
            // 删除EndOwner，使用专门的EndOwner删除处理
            log.info("删除EndOwner节点 {}，有 {} 个指向节点", nodeId, pointingNodes.size());
            for (BaseNodeCanvas pointing : pointingNodes) {
                log.info("指向EndOwner的节点: {}", pointing.getId());
            }
            handleEndOwnerDeletion(workingCopy, nodeToDelete, pointingNodes);
        } else {
            // 删除普通节点，更新所有指向它的节点
            for (BaseNodeCanvas pointingNode : pointingNodes) {
                if (pointingNode instanceof IRoutable routablePointing) {
                    routablePointing.setNextId(nextId);
                    nodeBatchExecutor.updateNode(pointingNode.getId(), pointingNode);
                }
            }

            // 更新后继节点的前驱
            if (nextId != null && !nextId.trim().isEmpty()) {
                BaseNodeCanvas nextNode = workingCopy.getFlowNodeMap().get(nextId);
                if (nextNode instanceof IRoutable routableNext && !pointingNodes.isEmpty()) {
                    // 如果有多个指向节点，选择第一个作为新的前驱
                    routableNext.setPrveId(pointingNodes.get(0).getId());
                    nodeBatchExecutor.updateNode(nextId, nextNode);
                }
            }
        }

        nodeBatchExecutor.deleteNode(nodeId);

        log.debug("删除节点 {}，是否为EndOwner: {}", nodeId, isEndOwner);
    }
    
    /**
     * 更新节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(String nodeId, Map<String, Object> updates) {
        if (nodeBatchExecutor == null || nodeId == null || updates == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas node = workingCopy.getFlowNodeMap().get(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到节点: " + nodeId);
        }

        // 应用更新
        applyUpdatesToNode(node, updates);

        nodeBatchExecutor.updateNode(nodeId, node);

        // 检查是否需要连接到结束
        Boolean connectToEnd = (Boolean) updates.get("connectToEnd");
        if (Boolean.TRUE.equals(connectToEnd) && node instanceof IRoutable) {
            autoWireStrategy.connectToEnd(workingCopy, node);
        }

        log.debug("更新节点 {}", nodeId);
    }

    /**
     * 处理EndOwner删除
     * 使用EndOwnerManager来确保正确的EndOwner转移
     */
    private void handleEndOwnerDeletion(ProcessNode processNode, BaseNodeCanvas endOwnerToDelete, List<BaseNodeCanvas> prevNodes) {
        // 使用AutoWireStrategy的EndOwnerManager来处理EndOwner删除
        try {
            log.info("处理EndOwner删除: {}，有 {} 个前驱节点", endOwnerToDelete.getId(), prevNodes.size());

            // 删除EndOwner时不需要调用abortEndOwnerIfFlatten，直接处理前驱连接

            // 如果有前驱节点，需要重新连接
            if (!prevNodes.isEmpty()) {
                // 选择最后一个前驱作为新的EndOwner
                BaseNodeCanvas newEndOwner = prevNodes.get(prevNodes.size() - 1);
                log.info("选择新的EndOwner: {}", newEndOwner.getId());
                if (newEndOwner instanceof IRoutable routableNewEnd) {
                    // 先清除当前EndOwner的状态，这样connectToEnd就会认为没有EndOwner
                    if (endOwnerToDelete instanceof IRoutable routableEndOwner) {
                        routableEndOwner.setNextId("");  // 临时清除EndOwner状态
                        log.info("临时清除被删除EndOwner的状态");
                    }

                    // 使用AutoWireStrategy连接到结束
                    log.info("调用connectToEnd设置新EndOwner");
                    autoWireStrategy.connectToEnd(processNode, newEndOwner);
                    log.info("新EndOwner {} 的nextId现在是: {}", newEndOwner.getId(), routableNewEnd.getNextId());

                    // 重要：记录新EndOwner的变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(newEndOwner.getId(), newEndOwner);
                    log.info("已记录新EndOwner的变更到NodeBatchExecutor");

                    // 其他前驱指向新的EndOwner（形成扇入）
                    for (int i = 0; i < prevNodes.size() - 1; i++) {
                        BaseNodeCanvas prevNode = prevNodes.get(i);
                        if (prevNode instanceof IRoutable routablePrev) {
                            routablePrev.setNextId(newEndOwner.getId());
                            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                        }
                    }

                    log.debug("删除EndOwner {}，重新选择EndOwner: {}，{} 个前驱扇入",
                             endOwnerToDelete.getId(), newEndOwner.getId(), prevNodes.size() - 1);
                }
            } else {
                log.warn("删除EndOwner {} 后没有前驱节点", endOwnerToDelete.getId());
            }
        } catch (Exception e) {
            log.error("处理EndOwner删除时发生错误", e);
            throw new RuntimeException("EndOwner删除处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 应用更新到节点
     */
    private void applyUpdatesToNode(BaseNodeCanvas node, Map<String, Object> updates) {
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            switch (key) {
                case "name":
                    node.setName((String) value);
                    break;
                case "nextId":
                    if (node instanceof IRoutable routableNode) {
                        routableNode.setNextId((String) value);
                    }
                    break;
                case "prveId":
                    if (node instanceof IRoutable routableNode) {
                        routableNode.setPrveId((String) value);
                    }
                    break;
                default:
                    log.warn("未知的更新属性: {}", key);
                    break;
            }
        }
    }
    
    /**
     * 移动节点到新位置
     * @param processNode 流程节点
     * @param nodeId 要移动的节点ID
     * @param newAfterNodeId 新的前驱节点ID
     * @deprecated 此方法尚未重构为使用nodeBatchExecutor
     */
    @Deprecated
    public void moveNode(ProcessNode processNode, String nodeId, String newAfterNodeId) {
        if (processNode == null || nodeId == null || newAfterNodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNodeCanvas nodeToMove = processNode.getFlowNodeMap().get(nodeId);
        if (nodeToMove == null) {
            throw new IllegalArgumentException("找不到要移动的节点: " + nodeId);
        }
        
        // 先断开原有连接
        autoWireStrategy.detach(processNode, nodeToMove, nodeToMove);
        
        // 连接到新位置
        BaseNodeCanvas newAfterNode = processNode.getFlowNodeMap().get(newAfterNodeId);
        if (newAfterNode instanceof IRoutable routableAfter) {
            String originalNext = routableAfter.getNextId();
            autoWireStrategy.spliceBetween(processNode, Arrays.asList(newAfterNode),
                                         nodeToMove, nodeToMove, originalNext);
        }
        
        log.debug("移动节点 {} 到节点 {} 之后", nodeId, newAfterNodeId);
    }

    /**
     * 查找所有指向指定节点的节点
     * @param processNode 流程节点
     * @param targetNodeId 目标节点ID
     * @return 指向目标节点的所有节点列表
     */
    private List<BaseNodeCanvas> findAllNodesPointingTo(ProcessNode processNode, String targetNodeId) {
        List<BaseNodeCanvas> pointingNodes = new ArrayList<>();

        if (processNode == null || targetNodeId == null || processNode.getFlowNodeMap() == null) {
            return pointingNodes;
        }

        for (BaseNodeCanvas node : processNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routableNode) {
                String nextId = routableNode.getNextId();
                if (targetNodeId.equals(nextId)) {
                    pointingNodes.add(node);
                }
            }
        }

        return pointingNodes;
    }
}
