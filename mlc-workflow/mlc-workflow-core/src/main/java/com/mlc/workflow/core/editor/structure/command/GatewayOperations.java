package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

/**
 * 网关操作命令
 * 实现网关的新增、删除、类型切换等操作
 */
@Slf4j
public class GatewayOperations {
    
    private final AutoWireStrategy autoWireStrategy;
    private final NodeBatchExecutor nodeBatchExecutor;
    
    public GatewayOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.autoWireStrategy = autoWireStrategy;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }
    
    /**
     * 放置策略枚举
     */
    public enum PlacementStrategy {
        LEFT_PLACEMENT,  // 左侧放置（移动原有链段到左分支）
        NO_MOVE         // 不移动（在原位置插入网关）
    }
    
    /**
     * 新增网关（默认并行）
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId, PlacementStrategy placement) {
        return addGateway(atNodeId, placement, GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
    }

    /**
     * 新增网关
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @param gatewayType 网关类型
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(String atNodeId,
                                PlacementStrategy placement, Integer gatewayType) {
        if (nodeBatchExecutor == null || atNodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 从工作副本获取节点
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas atNode = workingCopy.getFlowNodeMap().get(atNodeId);
        if (atNode == null) {
            throw new IllegalArgumentException("找不到插入点节点: " + atNodeId);
        }

        if (!(atNode instanceof IRoutable routableAtNode)) {
            throw new IllegalArgumentException("插入点节点必须是可路由的");
        }

        String originalNextId = routableAtNode.getNextId();
        
        // 创建网关和分支叶子
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName("网关");
        gateway.setGatewayType(gatewayType); // 设置网关类型

        ConditionNodeCanvas leftBranch = new ConditionNodeCanvas();
        leftBranch.setName("分支1");

        ConditionNodeCanvas rightBranch = new ConditionNodeCanvas();
        rightBranch.setName("分支2");

        // 如果是唯一分支网关，为分支叶子设置默认条件
        if (gatewayType == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            setDefaultConditionForBranch(leftBranch, 0);
            setDefaultConditionForBranch(rightBranch, 1);
        }

        // 设置网关的分支
        gateway.getFlowIds().add(leftBranch.getId());
        gateway.getFlowIds().add(rightBranch.getId());

        // 设置分支叶子的前驱
        leftBranch.setPrveId(gateway.getId());
        rightBranch.setPrveId(gateway.getId());

        nodeBatchExecutor.createNode(gateway);
        nodeBatchExecutor.createNode(leftBranch);
        nodeBatchExecutor.createNode(rightBranch);

        // 根据放置策略处理
        if (placement == PlacementStrategy.LEFT_PLACEMENT) {
            handleLeftPlacement(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        } else {
            handleNoMove(workingCopy, routableAtNode, gateway, leftBranch, rightBranch, originalNextId);
        }
        
        log.debug("在节点 {} 后新增网关 {}，策略: {}", atNodeId, gateway.getId(), placement);
        
        return gateway;
    }
    
    /**
     * 处理左侧放置策略
     * 根据设计方案：在网关中无论插入网关还是分支始终保持相对最上级网关为nextId=99
     */
    private void handleLeftPlacement(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
                                   ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        if (originalNextId != null && !originalNextId.trim().isEmpty()) {
            if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
                // 原来指向结束，根据设计方案：在网关中保持相对最上级网关为nextId=99
                // 将左分支连接到结束，但通过网关的nextId来实现
                leftBranch.setNextId(""); // 分支内部结束为空
                rightBranch.setNextId(""); // 右分支也为空

                // 网关本身指向结束，保持EndOwner在网关层级
                // 注意：atNode.setNextId(gateway.getId()) 已经在上面设置了，这里直接设置网关为EndOwner
                gateway.setNextId(EndOwnerManager.END_OWNER_ID);

                log.debug("左侧放置：原节点指向结束，网关 {} 成为新的EndOwner载体", gateway.getId());
            } else {
                // 将原有的下游链段移动到左分支
                leftBranch.setNextId(originalNextId);

                // 更新原下游节点的前驱
                BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
                if (originalNext instanceof IRoutable routableNext) {
                    routableNext.setPrveId(leftBranch.getId());
                }

                // 网关合流后继续原有流程
                String branchTailNext = findBranchTailNext(processNode, originalNextId);
                gateway.setNextId(branchTailNext);

                log.debug("左侧放置：移动原链段到左分支，网关合流到 {}", branchTailNext);
            }
        } else {
            // 原来没有下游，两个分支都为空
            leftBranch.setNextId("");
            rightBranch.setNextId("");
            gateway.setNextId("");
        }

        // 右分支设为空分支
        rightBranch.setNextId("");
    }
    
    /**
     * 处理不移动策略
     * 根据设计方案：在网关中保持EndOwner在网关层级
     */
    private void handleNoMove(ProcessNode processNode, IRoutable atNode, GatewayNodeCanvas gateway,
                            ConditionNodeCanvas leftBranch, ConditionNodeCanvas rightBranch, String originalNextId) {
        // 连接atNode到网关
        atNode.setNextId(gateway.getId());
        gateway.setPrveId(atNode.getId());

        // 记录节点变更
        nodeBatchExecutor.updateNode(atNode.getId(), (BaseNodeCanvas) atNode);

        // 处理网关的下游连接
        if (EndOwnerManager.END_OWNER_ID.equals(originalNextId)) {
            // 原来指向结束，根据设计方案：网关层级保持EndOwner
            // 注意：atNode.setNextId(gateway.getId()) 已经在上面设置了，这里直接设置网关为EndOwner
            gateway.setNextId(EndOwnerManager.END_OWNER_ID);
            log.debug("不移动策略：网关 {} 连接到结束", gateway.getId());
        } else {
            // 网关合流后继续原有下游
            gateway.setNextId(originalNextId);

            // 更新原下游节点的前驱
            if (originalNextId != null && !originalNextId.trim().isEmpty()) {
                BaseNodeCanvas originalNext = processNode.getFlowNodeMap().get(originalNextId);
                if (originalNext instanceof IRoutable routableNext) {
                    routableNext.setPrveId(gateway.getId());
                }
            }

            log.debug("不移动策略：网关 {} 合流到 {}", gateway.getId(), originalNextId);
        }

        // 两个分支都设为空分支（等待后续添加内容）
        leftBranch.setNextId("");
        rightBranch.setNextId("");
    }
    
    /**
     * 查找分支尾部的下一个节点
     */
    private String findBranchTailNext(ProcessNode processNode, String startNodeId) {
        Set<String> visited = new HashSet<>();
        return findBranchTailNextRecursive(processNode, startNodeId, visited);
    }
    
    private String findBranchTailNextRecursive(ProcessNode processNode, String currentNodeId, Set<String> visited) {
        if (currentNodeId == null || visited.contains(currentNodeId)) {
            return "";
        }
        
        visited.add(currentNodeId);
        BaseNodeCanvas currentNode = processNode.getFlowNodeMap().get(currentNodeId);
        
        if (currentNode instanceof IRoutable routableNode) {
            String nextId = routableNode.getNextId();
            if (nextId == null || nextId.trim().isEmpty() || EndOwnerManager.END_OWNER_ID.equals(nextId)) {
                return nextId;
            }
            return findBranchTailNextRecursive(processNode, nextId, visited);
        }
        
        return "";
    }
    
    /**
     * 删除网关
     * @param gatewayId 网关ID
     */
    public void deleteGateway(String gatewayId) {
        if (nodeBatchExecutor == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null || flowIds.isEmpty()) {
            throw new IllegalStateException("网关没有分支，无法删除");
        }

        if (flowIds.size() > 1) {
            throw new IllegalStateException("网关有多个分支，请先删除分支至只剩1条");
        }

        // 只剩一条分支，执行扁平化
        String remainingBranchId = flowIds.get(0);
        flattenGateway(workingCopy, gateway, remainingBranchId);

        nodeBatchExecutor.deleteNode(gatewayId);

        log.debug("删除网关 {}，已扁平化", gatewayId);
    }
    
    /**
     * 扁平化网关（将单分支网关替换为分支链）
     * 根据设计方案：触发AbortEndOwnerIfFlatten检查
     */
    private void flattenGateway(ProcessNode processNode, GatewayNodeCanvas gateway, String branchLeafId) {
        // 修复分支叶子的连接（如果它指向网关的话）
        // 重要：从NodeBatchExecutor获取最新的工作副本，确保获取到最新的节点状态
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNodeCanvas branchLeaf = workingCopy.getFlowNodeMap().get(branchLeafId);
        if (branchLeaf instanceof IRoutable routableLeaf) {
            String leafNextId = routableLeaf.getNextId();
            log.info("flattenGateway: 分支叶子 {} 的当前nextId: {}", branchLeafId, leafNextId);
            if (gateway.getId().equals(leafNextId)) {
                // 分支叶子指向网关（等待合流），需要连接到网关的下游
                String gatewayNextId = gateway.getNextId();
                if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                    // 网关是EndOwner，分支叶子应该成为新的EndOwner
                    routableLeaf.setNextId(EndOwnerManager.END_OWNER_ID);
                    log.debug("扁平化：分支叶子 {} 成为新的EndOwner", branchLeafId);
                } else {
                    // 网关不是EndOwner，分支叶子连接到网关的下游
                    routableLeaf.setNextId(gatewayNextId);
                    log.debug("扁平化：分支叶子 {} 连接到网关下游: {}", branchLeafId, gatewayNextId);
                }
                // 重要：记录变更到NodeBatchExecutor
                nodeBatchExecutor.updateNode(branchLeafId, branchLeaf);
            } else if (leafNextId == null || leafNextId.trim().isEmpty()) {
                // 分支叶子为空（等待合流），需要连接到网关的下游
                String gatewayNextId = gateway.getNextId();
                log.info("flattenGateway: 空分支叶子 {} 需要连接到网关下游: {}", branchLeafId, gatewayNextId);
                if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                    // 网关是EndOwner，分支叶子应该成为新的EndOwner
                    routableLeaf.setNextId(EndOwnerManager.END_OWNER_ID);
                    log.info("flattenGateway: 空分支叶子 {} 成为新的EndOwner", branchLeafId);
                } else {
                    // 网关不是EndOwner，分支叶子连接到网关的下游
                    routableLeaf.setNextId(gatewayNextId);
                    log.debug("扁平化：空分支叶子 {} 连接到网关下游: {}", branchLeafId, gatewayNextId);
                }

                // 修复分支叶子的前驱关系：连接到网关的前驱
                String gatewayPrevId = gateway.getPrveId();
                if (gatewayPrevId != null && !gatewayPrevId.trim().isEmpty()) {
                    routableLeaf.setPrveId(gatewayPrevId);
                    log.debug("扁平化：分支叶子 {} 的前驱设置为网关前驱: {}", branchLeafId, gatewayPrevId);

                    // 更新网关前驱节点的nextId，指向分支叶子
                    BaseNodeCanvas gatewayPrev = workingCopy.getFlowNodeMap().get(gatewayPrevId);
                    if (gatewayPrev instanceof IRoutable routablePrev) {
                        routablePrev.setNextId(branchLeafId);
                        nodeBatchExecutor.updateNode(gatewayPrevId, gatewayPrev);
                        log.debug("扁平化：网关前驱 {} 的nextId设置为分支叶子: {}", gatewayPrevId, branchLeafId);
                    }
                }

                // 重要：记录变更到NodeBatchExecutor
                nodeBatchExecutor.updateNode(branchLeafId, branchLeaf);
            }
        }

        // 获取分支链
        List<BaseNodeCanvas> branchChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);

        if (branchChain.isEmpty()) {
            // 分支为空，直接用网关的下游替换
            log.info("扁平化：分支链为空，直接连接前驱到后继");
            String gatewayNextId = gateway.getNextId();

            // 使用AutoWireStrategy替换网关为空（直接连接前驱到后继）
            List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, gateway.getId());
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                        // 网关原来指向结束，前驱节点连接到结束
                        autoWireStrategy.connectToEnd(processNode, prevNode);
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                    } else {
                        routablePrev.setNextId(gatewayNextId);
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                        // 更新下游节点的前驱
                        if (gatewayNextId != null && !gatewayNextId.trim().isEmpty()) {
                            BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(gatewayNextId);
                            if (nextNode instanceof IRoutable routableNext) {
                                routableNext.setPrveId(prevNode.getId());
                                // 重要：记录变更到NodeBatchExecutor
                                nodeBatchExecutor.updateNode(nextNode.getId(), nextNode);
                            }
                        }
                    }
                }
            }

            log.debug("扁平化网关 {}，分支为空，直接连接前驱到后继", gateway.getId());
        } else {
            // 分支不为空，用分支链替换网关
            BaseNodeCanvas branchHead = branchChain.get(0);
            BaseNodeCanvas branchTail = branchChain.get(branchChain.size() - 1);

            log.info("扁平化网关 {}，分支链: {} -> {}，链长度: {}", gateway.getId(), branchHead.getId(), branchTail.getId(), branchChain.size());

            // 手动处理网关替换：连接前驱到分支头，分支尾到下游
            List<BaseNodeCanvas> prevNodes = WorkflowQueryService.findPrevNodes(processNode, gateway.getId());
            for (BaseNodeCanvas prevNode : prevNodes) {
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(branchHead.getId());
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                }
            }

            // 更新分支头的前驱
            if (branchHead instanceof IRoutable routableHead) {
                if (!prevNodes.isEmpty()) {
                    routableHead.setPrveId(prevNodes.get(0).getId());
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                }
            }

            // 更新分支链中所有节点到NodeBatchExecutor
            // 注意：跳过分支叶子，因为它可能已经在前面被修改过了
            for (BaseNodeCanvas chainNode : branchChain) {
                if (!chainNode.getId().equals(branchLeafId)) {
                    // 只更新非分支叶子的节点，避免覆盖之前的修改
                    nodeBatchExecutor.updateNode(chainNode.getId(), chainNode);
                    log.debug("更新分支链节点: {}", chainNode.getId());
                } else {
                    log.debug("跳过分支叶子节点 {}，避免覆盖之前的修改", chainNode.getId());
                }
            }

            // 如果分支尾的nextId为空（等待合流），需要处理
            // 重新从工作副本获取分支尾部的最新状态
            BaseNodeCanvas latestBranchTail = workingCopy.getFlowNodeMap().get(branchTail.getId());
            if (latestBranchTail instanceof IRoutable routableTail) {
                String branchTailNext = routableTail.getNextId();
                log.info("扁平化：分支尾部 {} 的最新nextId: {}", branchTail.getId(), branchTailNext);
                if (branchTailNext == null || branchTailNext.trim().isEmpty()) {
                    // 分支尾原来等待合流，现在需要连接到网关的下游
                    // 这个逻辑在下面的gatewayNextId处理中会设置
                    log.info("扁平化：分支尾部为空，需要在下面处理");
                } else {
                    log.info("扁平化：分支尾部不为空，nextId: {}，需要重新连接前驱", branchTailNext);

                    // 查找所有指向网关的节点，将它们连接到分支头
                    List<BaseNodeCanvas> gatewayPrevNodes = WorkflowQueryService.findPrevNodes(workingCopy, gateway.getId());
                    log.info("扁平化：找到 {} 个节点指向网关", gatewayPrevNodes.size());

                    // 调试：检查所有节点的nextId
                    log.info("调试：工作副本中共有 {} 个节点", workingCopy.getFlowNodeMap().size());
                    for (BaseNodeCanvas node : workingCopy.getFlowNodeMap().values()) {
                        if (node instanceof IRoutable routableNode) {
                            String nextId = routableNode.getNextId();
                            log.info("调试：节点 {} (类型: {}) 的nextId: {}", node.getId(), node.getClass().getSimpleName(), nextId);
                            if (gateway.getId().equals(nextId)) {
                                log.info("调试：找到指向网关的节点: {} -> {}", node.getId(), gateway.getId());
                                gatewayPrevNodes.add(node); // 手动添加到列表中
                            }
                        }
                    }

                    // 处理所有指向网关的节点
                    for (BaseNodeCanvas prevNode : gatewayPrevNodes) {
                        if (prevNode instanceof IRoutable routablePrev) {
                            log.info("扁平化：将前驱节点 {} 从网关 {} 重新连接到分支头 {}",
                                    prevNode.getId(), gateway.getId(), branchHead.getId());
                            routablePrev.setNextId(branchHead.getId());
                            nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
                        }
                    }

                    // 额外检查：确保所有已经指向分支头的节点也被保存到NodeBatchExecutor
                    BaseNodeCanvas actualPrevNode = null;
                    for (BaseNodeCanvas node : workingCopy.getFlowNodeMap().values()) {
                        if (node instanceof IRoutable routableNode) {
                            String nextId = routableNode.getNextId();
                            if (branchHead.getId().equals(nextId)) {
                                log.info("扁平化：保存已连接到分支头的节点 {} 到NodeBatchExecutor，节点nextId: {}",
                                        node.getId(), nextId);
                                nodeBatchExecutor.updateNode(node.getId(), node);

                                // 验证更新后的状态
                                ProcessNode updatedWorkingCopy = nodeBatchExecutor.getWorkingCopy();
                                BaseNodeCanvas updatedNode = updatedWorkingCopy.getFlowNodeMap().get(node.getId());
                                if (updatedNode instanceof IRoutable updatedRoutable) {
                                    log.info("扁平化：更新后工作副本中节点 {} 的nextId: {}",
                                            node.getId(), updatedRoutable.getNextId());
                                }

                                if (actualPrevNode == null) {
                                    actualPrevNode = node; // 记录第一个前驱节点
                                }
                            }
                        }
                    }

                    // 如果找到了实际的前驱节点，设置分支头的前驱
                    if (actualPrevNode != null && branchHead instanceof IRoutable routableHead) {
                        routableHead.setPrveId(actualPrevNode.getId());
                        nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                        log.info("扁平化：分支头 {} 的前驱设置为实际前驱 {}", branchHead.getId(), actualPrevNode.getId());
                    }

                    // 更新分支头的前驱
                    if (branchHead instanceof IRoutable routableHead && !gatewayPrevNodes.isEmpty()) {
                        routableHead.setPrveId(gatewayPrevNodes.get(0).getId());
                        nodeBatchExecutor.updateNode(branchHead.getId(), branchHead);
                        log.info("扁平化：分支头 {} 的前驱设置为 {}", branchHead.getId(), gatewayPrevNodes.get(0).getId());
                    }

                    // 直接跳到最后的EndOwner检查
                    log.debug("网关 {} 扁平化完成，保留分支链", gateway.getId());
                    // 注意：在扁平化情况下，不调用abortEndOwnerIfFlatten，因为我们已经正确设置了连接
                    return;
                }
            }

            // 处理网关原来的下游连接
            // 重新获取网关对象，确保获取到最新的状态
            GatewayNodeCanvas latestGateway = WorkflowQueryService.findGateway(workingCopy, gateway.getId());
            String gatewayNextId = latestGateway != null ? latestGateway.getNextId() : gateway.getNextId();
            log.debug("扁平化：网关 {} 的最新nextId: {}", gateway.getId(), gatewayNextId);
            if (branchTail instanceof IRoutable routableTail) {
                if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                    // 网关原来指向结束，分支尾部应该成为EndOwner
                    // 检查分支尾部是否已经是EndOwner
                    if (!EndOwnerManager.END_OWNER_ID.equals(routableTail.getNextId())) {
                        // 分支尾部还不是EndOwner，设置为EndOwner
                        routableTail.setNextId(EndOwnerManager.END_OWNER_ID);
                        log.debug("扁平化：分支尾部 {} 成为EndOwner", branchTail.getId());
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                    } else {
                        log.debug("扁平化：分支尾部 {} 已经是EndOwner，无需修改", branchTail.getId());
                    }
                } else if (gatewayNextId != null && !gatewayNextId.trim().isEmpty()) {
                    // 分支尾部连接到网关的原下游
                    routableTail.setNextId(gatewayNextId);
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                    // 更新下游节点的前驱
                    BaseNodeCanvas nextNode = processNode.getFlowNodeMap().get(gatewayNextId);
                    if (nextNode instanceof IRoutable routableNext) {
                        routableNext.setPrveId(branchTail.getId());
                        // 重要：记录变更到NodeBatchExecutor
                        nodeBatchExecutor.updateNode(nextNode.getId(), nextNode);
                    }
                } else {
                    // 网关原来没有下游，分支尾部也设为空
                    routableTail.setNextId("");
                    // 重要：记录变更到NodeBatchExecutor
                    nodeBatchExecutor.updateNode(branchTail.getId(), branchTail);
                }
            }

            log.debug("网关 {} 扁平化完成，保留分支链", gateway.getId());
        }

        // 触发EndOwner检查（根据设计方案的AbortEndOwnerIfFlatten）
        autoWireStrategy.abortEndOwnerIfFlatten(workingCopy);
    }
    
    /**
     * 修改网关类型
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(String gatewayId, Integer toType) {
        if (nodeBatchExecutor == null || gatewayId == null || toType == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 使用网关语义策略处理类型切换
        GatewaySemanticsStrategy.switchGatewayType(workingCopy, gateway, toType);

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 更新所有被修改的分支节点
        List<String> flowIds = gateway.getFlowIds();
        if (flowIds != null) {
            for (String flowId : flowIds) {
                BaseNodeCanvas branchNode = workingCopy.getFlowNodeMap().get(flowId);
                if (branchNode != null) {
                    nodeBatchExecutor.updateNode(flowId, branchNode);
                    log.debug("更新分支节点: {}", flowId);
                }
            }
        }

        log.debug("网关 {} 类型已切换为 {}", gatewayId, toType);
    }

    /**
     * 为分支设置默认条件
     *
     * @param branch 分支节点
     * @param index 分支索引
     */
    private void setDefaultConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == 2 - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
