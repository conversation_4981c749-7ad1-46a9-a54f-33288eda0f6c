package com.mlc.workflow.core.editor.structure.utils;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 参数校验工具类
 * 统一参数校验逻辑，减少重复代码
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ValidationUtils {

    /**
     * 校验ProcessNode不为空
     * @param processNode 流程节点
     * @param paramName 参数名称
     * @throws IllegalArgumentException 如果参数为空
     */
    public static void requireNonNull(ProcessNode processNode, String paramName) {
        if (processNode == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验BaseNodeCanvas不为空
     * @param node 节点
     * @param paramName 参数名称
     * @throws IllegalArgumentException 如果参数为空
     */
    public static void requireNonNull(BaseNodeCanvas node, String paramName) {
        if (node == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验字符串不为空
     * @param str 字符串
     * @param paramName 参数名称
     * @throws IllegalArgumentException 如果参数为空或空字符串
     */
    public static void requireNonEmpty(String str, String paramName) {
        if (str == null || str.trim().isEmpty()) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验列表不为空
     * @param list 列表
     * @param paramName 参数名称
     * @throws IllegalArgumentException 如果参数为空
     */
    public static void requireNonNull(List<?> list, String paramName) {
        if (list == null) {
            throw new IllegalArgumentException(paramName + "不能为空");
        }
    }

    /**
     * 校验节点是否可路由
     * @param node 节点
     * @param paramName 参数名称
     * @return 可路由节点
     * @throws IllegalArgumentException 如果节点不可路由
     */
    public static IRoutable requireRoutable(BaseNodeCanvas node, String paramName) {
        requireNonNull(node, paramName);
        if (!(node instanceof IRoutable)) {
            throw new IllegalArgumentException(paramName + "必须是可路由的");
        }
        return (IRoutable) node;
    }

    /**
     * 校验节点在流程中存在
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param paramName 参数名称
     * @return 找到的节点
     * @throws IllegalArgumentException 如果节点不存在
     */
    public static BaseNodeCanvas requireNodeExists(ProcessNode processNode, String nodeId, String paramName) {
        requireNonNull(processNode, "processNode");
        requireNonEmpty(nodeId, "nodeId");
        
        BaseNodeCanvas node = processNode.getFlowNodeMap().get(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到" + paramName + "节点: " + nodeId);
        }
        return node;
    }

    /**
     * 校验AutoWireStrategy的基本参数
     * @param processNode 流程节点
     * @param head 头节点
     * @param tail 尾节点
     */
    public static void validateAutoWireParams(ProcessNode processNode, BaseNodeCanvas head, BaseNodeCanvas tail) {
        requireNonNull(processNode, "processNode");
        requireNonNull(head, "head");
        requireNonNull(tail, "tail");
    }

    /**
     * 校验网关操作的基本参数
     * @param nodeId 节点ID
     * @param paramName 参数名称
     */
    public static void validateGatewayOperationParams(String nodeId, String paramName) {
        requireNonEmpty(nodeId, paramName);
    }

    /**
     * 校验分支操作的基本参数
     * @param gatewayId 网关ID
     * @param branchId 分支ID（可选）
     */
    public static void validateBranchOperationParams(String gatewayId, String branchId) {
        requireNonEmpty(gatewayId, "gatewayId");
        if (branchId != null) {
            requireNonEmpty(branchId, "branchId");
        }
    }

    /**
     * 校验位置参数
     * @param position 位置
     * @param maxPosition 最大位置
     */
    public static void validatePosition(int position, int maxPosition) {
        if (position < 0 || position > maxPosition) {
            throw new IllegalArgumentException("位置参数无效: " + position + "，应在0-" + maxPosition + "之间");
        }
    }

    /**
     * 校验网关类型
     * @param gatewayType 网关类型
     */
    public static void validateGatewayType(Integer gatewayType) {
        if (gatewayType == null) {
            throw new IllegalArgumentException("网关类型不能为空");
        }
        if (!GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL.equals(gatewayType) &&
            !GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE.equals(gatewayType)) {
            throw new IllegalArgumentException("不支持的网关类型: " + gatewayType);
        }
    }

    /**
     * 创建标准错误消息
     * @param operation 操作名称
     * @param nodeId 节点ID
     * @param reason 失败原因
     * @return 错误消息
     */
    public static String createErrorMessage(String operation, String nodeId, String reason) {
        return String.format("%s失败，节点: %s，原因: %s", operation, nodeId, reason);
    }

    /**
     * 创建标准成功消息
     * @param operation 操作名称
     * @param nodeId 节点ID
     * @return 成功消息
     */
    public static String createSuccessMessage(String operation, String nodeId) {
        return String.format("%s成功，节点: %s", operation, nodeId);
    }

    /**
     * 校验批量执行器状态
     * @param executor 批量执行器
     * @throws IllegalStateException 如果执行器状态无效
     */
    public static void validateExecutorState(Object executor) {
        if (executor == null) {
            throw new IllegalStateException("批量执行器未初始化");
        }
    }

    /**
     * 安全获取节点ID
     * @param node 节点
     * @return 节点ID，如果节点为空则返回"null"
     */
    public static String safeGetNodeId(BaseNodeCanvas node) {
        return node != null ? node.getId() : "null";
    }

    /**
     * 安全获取节点名称
     * @param node 节点
     * @return 节点名称，如果节点为空或名称为空则返回默认值
     */
    public static String safeGetNodeName(BaseNodeCanvas node) {
        if (node == null) {
            return "未知节点";
        }
        String name = node.getName();
        return (name != null && !name.trim().isEmpty()) ? name : "未命名节点";
    }
}
