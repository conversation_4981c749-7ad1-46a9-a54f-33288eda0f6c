# 1. 模型与模式选型

**1.1 领域模型补全**

* `IRoutable`：抽象“可路由节点”（`id / nextId` 等）。
* `IHasBranches`：用于网关节点（`List<String> flowIds`、`gatewayType`）。
* `IHasConditions`：用于分支叶子（条件集合）。
* `IHasSubProcess`：用于含子流程的节点（如审批发起/子流程触发）。
* `ProcessNode`：`startEventId` + `flowNodeMap<String, Node>`。

**1.2 关键设计模式**

* **Composite（组合）**：`ProcessNode` + 节点形成“可遍历的有向结构”，子流程为“子树”。
* **Visitor（访问者）**：统一遍历不同节点类型，便于做一致的增删改（对网关、分支叶子、普通节点、子流程分别处理）。
* **Command（命令）**：每个操作封装为命令对象，便于事务管理与组合操作（不做 Undo/Redo）。
* **Strategy（策略）**：
    * **AutoWireStrategy**：通用“上下文自动连线”策略（断开、连接、替换、拼接 end 语义）。
    * **GatewaySemanticsStrategy**：并行与唯一分支的合流/触发语义封装（改网关类型时复用）。
* **Factory（工厂）**：创建网关、分支叶子、普通节点的统一入口（保证默认值与 id 生成一致）。
* **Unit of Work + Repository**：一次操作产生多处变更，集中提交与校验（保证原子性与不变量）。

---

# 2. 遍历与查询（非 DAG，面向流程语义）

实现 **TraverseService**（DFS深度优先，遇网关时对 `flowIds` 展开，遇子流程时递归进入其 `processNode`）：

* `visit(processNode, visitor)`：从 `startEventId` 开始，沿 `nextId` 直到 `99` 或空串规则。
* 维护 `VisitedSet` 防止异常回路（虽非 DAG，但业务应避免环；一旦检测到，进入“结构校验器”报错）。
* 常用查询：

    * `findPrevNodes(id)`：在 `flowNodeMap` 中搜索所有 `node.nextId == id` 的节点集合（支持网关/分支/普通节点）。
    * `branchHeads(gateway)`：由 `flowIds` 取分支叶子，顺着每个叶子 `nextId` 找到各自分支首节点（可能为 null/空，表示分支为空）。
    * `branchTails(gateway)`：各分支的“尾结点集合”（`nextId==""` 的那些）。

---

# 3. 通用“上下文自动连线”策略（CRUD 贯穿）

抽象为 **AutoWireStrategy**：

* **3.1 术语与不变量**

    * **Head/Tail**：一段链的首/尾节点。
    * **ContextPrev**：所有指向某段链 `Head.id` 的前驱集合。
    * **ContextNext**：某段链的后继节点 id（若是网关，则为网关的 `nextId`；若普通链段，则为尾结点的 `nextId`）。
    * **EndOwner 不变量**：同一 `ProcessNode` 内，**恰有 1 个**节点的 `nextId=99`。任何新增“指向结束”的请求，必须转化为“接到 EndOwner 之前”。在`nextId=99`对应的节点之后进行插入操作，自动把插入节点的 nextId 变为 99, 但是在网关中无论插入网关还是分支始终保持相对最上级网关为 `nextId=99`；

* **3.2 基本操作原语**

    1. **Detach(head, tail)**：

    * 找到 `ContextPrev`，将它们的 `nextId` 暂置为 `null`（或占位）；
    * 将 `tail.nextId` 置空（或占位）；
    2. **SpliceBetween(prevs, head, tail, next)**：

    * 将 `prevs[*].nextId = head.id`；
    * 将 `tail.nextId = next`（网关场景：若拼在网关下，按网关语义处理 `""`/合流）。
    3. **Replace(oldHead..oldTail, newHead..newTail)**：= `Detach(old)` + `SpliceBetween(prevsOfOld, newHead..newTail, oldContextNext)`。
    4. **ConnectToEnd(tail)**：

    * 若当前流程**无 EndOwner**：设置 `tail.nextId=99`，登记为 EndOwner。
    * 若已有 EndOwner（记为 `E`），**禁止再设 99**：设置 `tail.nextId = E.id`，形成“扇入”，保证仍只有 `E.nextId=99`。
    5. **AbortEndOwnerIfFlatten**（当结构“扁平化”导致 `E` 不再是流程实际末尾时）：

    * 对 `E` 的前驱重新打补丁，让**新末尾**成为 EndOwner（或保持 `E`，将其他尾部接到 `E`）。

> 以上原语被所有 CRUD 操作复用，统一处理“断开、连接、替换、接终点”等副作用。

---

# 4. 网关操作（新增、删除、改类型）

## 4.1 新增网关（默认并行）

**入口**：`addGateway(atNodeId, placement)`，`placement ∈ {LeftPlacement, NoMove}`
设 `A` 为插入点上方节点，原 `A.next=B`：

* **共有步骤**

    * 由工厂创建网关 `G(typeId=1, gatewayType=1)` 与两条**分支叶子** `L,R(typeId=2)`；
    * `G.flowIds=[L.id,R.id]`；`G.nextId = B.id`（汇合后的下游保持不变）。
    * `A.nextId = G.id`。
* **特殊：LeftPlacement（左侧放置）**

    * **移动**：将原 `B -> … ->（直至原流程末尾/汇合点前）` 整段链 **整体搬入 L 分支**：

        * 若原末尾指向 `99`：用 **Replace** 接到 `L`，并把 L 分支 **唯一**接入结束（见 EndOwner 规则）；
        * 若原末尾为普通 `next`：把该 `next` 作为**网关合流后**的下游（保持 `G.nextId` 语义），分支尾 `nextId=""`。
    * **R 分支**设为空分支（`R.nextId` 暂无，尾部 `""`）。
    * **结束连接**：若“需求要求两条默认分支都直连结束”，按 **EndOwner** 做“扇入”：

        * 令 `L` 的尾成为 **EndOwner**（`nextId=99`）；
        * `R` 的尾 `nextId = L.tail.id`（而非也设 `99`），保证单 EndOwner。
* **特殊：NoMove（不移动）**

    * 只是在 `A` 与 `B` 之间插入 `G + L + R`，`L/R` 暂为空分支；
    * 合流后继续走 `G.nextId=B`；等到后续“分支汇集后再执行 B”。

> 边界：若 `A` 或 `B` 在子流程中，以上操作作用于该子流程的 `processNode`，EndOwner 亦按子流程独立计算。

## 4.2 删除网关

`deleteGateway(gatewayId)`：

* 若 `G.flowIds` **>1**：拒绝直接删除，需先删分支至只剩 1 条（或先合并）。
* 若 **只剩 1 条分支**（满足你的 3.1.2）：

    * 取该分支链 `S`（从叶子 `nextId` 出发至尾部 `""`）；
    * **Flatten**：`Replace(G as oldHead..oldTail=G, newHead..newTail=S)`：

        * `prevs(G)` 全部接到 `S.head`；
        * `S.tail.nextId = G.nextId`（原合流后下游）；
    * 删除 `G` 和该分支叶子；
    * 触发 **AbortEndOwnerIfFlatten** 检查（若 `G` 曾与 `99` 有关联）。

## 4.3 修改网关类型（并行↔唯一）

`switchGatewayType(gatewayId, toType)`：

* **并行→唯一**：

    * 为每条分支叶子补齐默认条件（如“始终成立”）并允许用户再编辑；
    * 保持 `flowIds` 顺序；第一个默认为“优先分支”，最后一条可作为“else”。
* **唯一→并行**：

    * 清空/忽略条件（或迁移到分支元数据但不参与路由）；
    * 确保分支尾 `nextId=""` 的一致性。
* **不变量**：不改变 `G.nextId`（合流后下游不变）；维持 EndOwner 规则。

---

# 5. 分支操作（增/删/序/拷贝）

## 5.1 新增分支

`addBranch(gatewayId, position)`：

* 创建新分支叶子 `N(typeId=2)`，插入 `G.flowIds` 指定位置；
* `N.nextId` 为空（分支暂时为空链），尾 `""`；
* 若 `G.gatewayType=2`（唯一分支）：为 `N` 生成默认条件，且保证“else”唯一；
* 校验 EndOwner 未被破坏（分支为空链不涉及）。

## 5.2 删除分支

`deleteBranch(gatewayId, branchLeafId)`：

* 删除该叶子及其**整条分支链**：

    * 用 `Detach` 从所有前驱处切除；
    * 若分支链尾或中途节点曾是 EndOwner：执行 **AbortEndOwnerIfFlatten**，将其他尾部接回新的 EndOwner；
* 若删除后 `flowIds` 仅剩 1 条 → 触发 **4.2 删除网关** 的“扁平化逻辑”。

## 5.3 调整分支顺序

`reorderBranches(gatewayId, newOrder)`：

* 只重排 `flowIds` 列表；不改分支链内容与条件；
* 若唯一分支：同步更新“优先序”（影响匹配顺序）与“else”位置。

## 5.4 复制分支

`duplicateBranch(gatewayId, branchLeafId, position)`：

* 深拷贝该分支链（含所有普通节点与其参数、并生成**新 id**）；
* 若唯一分支：条件也拷贝，但需去重/提示冲突；
* 若原链尾是 EndOwner：**禁止**让拷贝链尾也设 `99`，而是令新尾 `nextId = 原尾.id`（扇入）。

---

# 6. 普通节点操作（增/删/改）

## 6.1 增加普通节点

`insertNode(afterNodeId, newNodeSpec)`：

* 取得 `A=afterNode` 与 `B=A.nextId`；
* 构造新节点 `N`，执行 `SpliceBetween([A], N, N, B)`；
* 若 `B=99`：则 `ConnectToEnd(N)`（扇入至 EndOwner）；
* 若处于分支链中：尾部仍以 `""` 等待合流（除非用户指定连到结束）。

## 6.2 删除普通节点

`deleteNode(nodeId)`：

* 找 `prevs(nodeId)` 与 `next=node.nextId`；
* 执行 `Replace(node, ∅)` 等价：`prevs[*].nextId = next`；
* 若该节点是 EndOwner：将 `prevs[*]` 中 **最后一条链的尾** 接为新 EndOwner（或选一个稳定策略，例如“最靠近结束的后继作为新的 EndOwner”）。

## 6.3 修改普通节点

* 仅更新节点元数据，不触及路由；若“修改导致应直连结束”，调用 `ConnectToEnd`。

---

# 7. 子流程（IHasSubProcess）的一致性

* 子流程拥有独立的 `ProcessNode` 与独立 **EndOwner**。
* 遍历/CRUD 递归进入子流程，但**不跨越**主流程与子流程的 EndOwner 限制。
* 删除或移动包含子流程的节点：
    * 若只是移动位置，不影响其内部 EndOwner；
    * 若删除该节点：**一并删除其子流程结构**（或转为“孤儿子流程池”再决定处置）。
* 示例中的“审批子流程”（`发起审批→审批→99`）就是独立终止于 `99` 的子流程。

---

# 8. 结构校验器（提交前的统一校验）

* **单流程单 EndOwner**：扫描 `flowNodeMap`，统计 `nextId=99` 的节点数量 **==1**；子流程各自也 ==1。
* **无悬空**：除 `startEventId` 外，每个节点至少有一个前驱（网关叶子除外）；所有 `nextId` 指向的 id 必须存在（`""/99` 例外）。
* **网关完整性**：`flowIds` 非空；唯一分支至少 1 条、最多 N 条；并行分支允许空链但尾部必须 `""`。
* **条件完备性**（唯一分支）：每条分支有条件，且“else”最多一条。
* **无环**：遍历检测环路；发现则拒绝提交。
* **子流程一致性**：对子流程递归执行同样校验。

---

# 9. 事务与持久化

* 使用 **Unit of Work** 收集一次操作产生的所有写集（节点新增/删除、nextId 改动、flowIds 改动等），**一次性提交**。

---

# 10. 关键边界与场景清单（落地测试用）

1. 在某普通节点下方 **LeftPlacement** 新增并行网关，且下游已有一个直连结束（`99`）的节点——验证 **EndOwner** 扇入策略。
2. 在唯一分支网关里 **复制分支**，新分支条件与已有分支冲突——应自动去重或标红。
3. 分支逐条删除直至只剩一条——触发“**删除网关**扁平化”逻辑。
4. 在子流程内部新增节点直连结束——不影响主流程 EndOwner。
5. 删除 EndOwner 节点——自动重选 EndOwner 并修复所有扇入。
6. 改网关类型（并行↔唯一）后，继续对分支进行增删序拷，验证条件与尾部 `""` 的健壮性。
7. 复杂链段 **Replace**（在网关前后各一段）——校验 `prevs` 与合流 `next` 被正确拼接。

---

# 11. 对外服务接口（建议的面向应用 API，非代码）

* `WorkflowEditor.addGateway(atNodeId, placement)`
* `WorkflowEditor.deleteGateway(gatewayId)`
* `WorkflowEditor.switchGatewayType(gatewayId, toType)`
* `WorkflowEditor.addBranch(gatewayId, position)` / `deleteBranch(gatewayId, branchLeafId)` / `reorderBranches(gatewayId, newOrder)` / `duplicateBranch(gatewayId, branchLeafId, position)`
* `WorkflowEditor.insertNode(afterNodeId, newNodeSpec)` / `deleteNode(nodeId)` / `updateNode(nodeId, patch)`
* `WorkflowValidator.validate(processNode)`（提交前强校验）
* `WorkflowQuery.findPrevNodes(id)` / `findGateway(id)` / `findBranchChain(leafId)` 等

---

# 12. 实施要点与落地建议

* **从“通用连线原语”入手**：先实现 `findPrevNodes / Detach / SpliceBetween / Replace / ConnectToEnd`，保证一切操作都走同一套拼接策略。
* **尽早引入校验器**：任何改动先过校验再落库，保底稳定性。
* **子流程一致走递归**：编辑入口带上“当前作用的 ProcessNode”，EndOwner 与校验都在局部生效。
* **网关变更的副作用**：全部封装在 `GatewaySemanticsStrategy` 内，避免散落在调用方。
