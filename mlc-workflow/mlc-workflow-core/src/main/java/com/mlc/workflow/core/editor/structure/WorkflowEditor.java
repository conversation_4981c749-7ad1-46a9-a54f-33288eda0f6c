package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.structure.command.BranchOperations;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.constants.WorkflowConstants;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.ValidationUtils;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

/**
 * 工作流编辑器
 * 对外服务接口，整合所有操作命令和校验器，提供统一的编辑入口
 */
@Slf4j
public class WorkflowEditor {
    
    private final GatewayOperations gatewayOperations;
    private final BranchOperations branchOperations;
    private final NodeOperations nodeOperations;
    private final NodeBatchExecutor nodeBatchExecutor;
    private final WorkflowValidator validator;

    public WorkflowEditor(GatewayOperations gatewayOperations, BranchOperations branchOperations,
                          NodeOperations nodeOperations, NodeBatchExecutor nodeBatchExecutor,
                          WorkflowValidator validator) {
        this.gatewayOperations = gatewayOperations;
        this.branchOperations = branchOperations;
        this.nodeOperations = nodeOperations;
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.validator = validator;
    }
    
    // ==================== 网关操作 ====================
    
    /**
     * 新增网关
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNodeCanvas addGateway(ProcessNode processNode, String atNodeId,
                                GatewayOperations.PlacementStrategy placement) {
        return executeInTransaction(processNode, "新增网关",
            () -> gatewayOperations.addGateway(atNodeId, placement));
    }
    
    /**
     * 删除网关
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     */
    public void deleteGateway(ProcessNode processNode, String gatewayId) {
        executeInTransactionVoid(processNode, "删除网关",
            () -> gatewayOperations.deleteGateway(gatewayId));
    }

    /**
     * 切换网关类型
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(ProcessNode processNode, String gatewayId, Integer toType) {
        executeInTransactionVoid(processNode, "切换网关类型",
            () -> gatewayOperations.switchGatewayType(gatewayId, toType));
    }

    // ==================== 分支操作 ====================
    
    /**
     * 新增分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param position 插入位置
     * @return 创建的分支叶子节点
     */
    public ConditionNodeCanvas addBranch(ProcessNode processNode, String gatewayId, int position) {
        nodeBatchExecutor.begin(processNode);
        try {
            ConditionNodeCanvas branch = branchOperations.addBranch(gatewayId, position);

            if (nodeBatchExecutor.commit()) {
                log.info("成功新增分支: {}", branch.getId());
                return branch;
            } else {
                throw new RuntimeException("新增分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(ProcessNode processNode, String gatewayId, String branchLeafId) {
        nodeBatchExecutor.begin(processNode);
        try {
            branchOperations.deleteBranch(gatewayId, branchLeafId);

            if (nodeBatchExecutor.commit()) {
                log.info("成功删除分支: {}", branchLeafId);
            } else {
                throw new RuntimeException("删除分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 调整分支顺序
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(ProcessNode processNode, String gatewayId, List<String> newOrder) {
        nodeBatchExecutor.begin(processNode);
        try {
            branchOperations.reorderBranches(gatewayId, newOrder);
            
            if (nodeBatchExecutor.commit()) {
                log.info("成功调整分支顺序: {}", gatewayId);
            } else {
                throw new RuntimeException("调整分支顺序失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 复制分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置
     * @return 复制的分支叶子节点
     */
    public ConditionNodeCanvas duplicateBranch(ProcessNode processNode, String gatewayId,
                                       String branchLeafId, int position) {
        nodeBatchExecutor.begin(processNode);
        try {
            ConditionNodeCanvas newBranch = branchOperations.duplicateBranch(gatewayId, branchLeafId, position);

            if (nodeBatchExecutor.commit()) {
                log.info("成功复制分支: {} -> {}", branchLeafId, newBranch.getId());
                return newBranch;
            } else {
                throw new RuntimeException("复制分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 普通节点操作 ====================
    
    /**
     * 插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param insertNode 新节点规格
     * @return 创建的节点
     */
    public BaseNodeCanvas insertNode(ProcessNode processNode, String afterNodeId, BaseNodeCanvas insertNode) {
        nodeBatchExecutor.begin(processNode);
        try {
            BaseNodeCanvas newNode = nodeOperations.insertNode(afterNodeId, insertNode);

            if (nodeBatchExecutor.commit()) {
                log.info("成功插入节点: {}", newNode.getId());
                return newNode;
            } else {
                throw new RuntimeException("插入节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除节点
     * @param processNode 流程节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(ProcessNode processNode, String nodeId) {
        nodeBatchExecutor.begin(processNode);
        try {
            nodeOperations.deleteNode(nodeId);

            if (nodeBatchExecutor.commit()) {
                log.info("成功删除节点: {}", nodeId);
            } else {
                throw new RuntimeException("删除节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 更新节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(ProcessNode processNode, String nodeId, Map<String, Object> updates) {
        nodeBatchExecutor.begin(processNode);
        try {
            nodeOperations.updateNode(nodeId, updates);

            if (nodeBatchExecutor.commit()) {
                log.info("成功更新节点: {}", nodeId);
            } else {
                throw new RuntimeException("更新节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 验证操作 ====================
    
    /**
     * 验证流程
     * @param processNode 流程节点
     * @return 验证结果
     */
    public WorkflowValidator.ValidationResult validate(ProcessNode processNode) {
        return validator.validate(processNode);
    }
    
    // ==================== 查询操作 ====================
    
    /**
     * 查找前驱节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 前驱节点列表
     */
    public List<BaseNodeCanvas> findPrevNodes(ProcessNode processNode, String nodeId) {
        return WorkflowQueryService.findPrevNodes(processNode, nodeId);
    }
    
    /**
     * 查找网关
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 网关节点
     */
    public GatewayNodeCanvas findGateway(ProcessNode processNode, String nodeId) {
        return WorkflowQueryService.findGateway(processNode, nodeId);
    }
    
    /**
     * 查找分支链
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @return 分支链中的所有节点
     */
    public List<BaseNodeCanvas> findBranchChain(ProcessNode processNode, String branchLeafId) {
        return WorkflowQueryService.findBranchChain(processNode, branchLeafId);
    }
    
    /**
     * 执行事务操作的模板方法
     * @param processNode 流程节点
     * @param operation 操作名称
     * @param action 具体操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    private <T> T executeInTransaction(ProcessNode processNode, String operation, Supplier<T> action) {
        ValidationUtils.requireNonNull(processNode, "processNode");
        ValidationUtils.requireNonEmpty(operation, "operation");

        nodeBatchExecutor.begin(processNode);
        try {
            T result = action.get();

            if (nodeBatchExecutor.commit()) {
                log.info(WorkflowConstants.formatSuccess(WorkflowConstants.SUCCESS_OPERATION, operation, ""));
                return result;
            } else {
                throw new RuntimeException(WorkflowConstants.formatError(
                    WorkflowConstants.ERROR_OPERATION_FAILED, operation, "", "提交失败"));
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 执行无返回值的事务操作
     * @param processNode 流程节点
     * @param operation 操作名称
     * @param action 具体操作
     */
    private void executeInTransactionVoid(ProcessNode processNode, String operation, Runnable action) {
        executeInTransaction(processNode, operation, () -> {
            action.run();
            return null;
        });
    }

}
