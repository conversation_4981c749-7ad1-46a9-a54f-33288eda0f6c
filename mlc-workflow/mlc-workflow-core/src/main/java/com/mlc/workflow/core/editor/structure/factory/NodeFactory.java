package com.mlc.workflow.core.editor.structure.factory;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 节点工厂
 * 实现设计方案中的Factory模式，统一节点创建入口，保证默认值与ID生成一致
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class NodeFactory {

    /**
     * 创建网关节点
     * @param gatewayType 网关类型
     * @param name 网关名称
     * @return 网关节点
     */
    public static GatewayNodeCanvas createGateway(Integer gatewayType, String name) {
        GatewayNodeCanvas gateway = new GatewayNodeCanvas();
        gateway.setName(name != null ? name : "网关");
        gateway.setGatewayType(gatewayType != null ? gatewayType : GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
        
        log.debug("创建网关节点: {} (类型: {})", gateway.getId(), gateway.getGatewayType());
        return gateway;
    }

    /**
     * 创建默认并行网关
     * @return 并行网关节点
     */
    public static GatewayNodeCanvas createParallelGateway() {
        return createGateway(GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL, "并行网关");
    }

    /**
     * 创建默认唯一分支网关
     * @return 唯一分支网关节点
     */
    public static GatewayNodeCanvas createExclusiveGateway() {
        return createGateway(GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE, "唯一分支网关");
    }

    /**
     * 创建分支叶子节点
     * @param name 分支名称
     * @param gatewayId 所属网关ID
     * @return 分支叶子节点
     */
    public static ConditionNodeCanvas createBranchLeaf(String name, String gatewayId) {
        ConditionNodeCanvas branch = new ConditionNodeCanvas();
        branch.setName(name != null ? name : "分支");
        if (gatewayId != null) {
            branch.setPrveId(gatewayId);
        }
        
        log.debug("创建分支叶子节点: {} (网关: {})", branch.getId(), gatewayId);
        return branch;
    }

    /**
     * 创建带默认条件的分支叶子节点（用于唯一分支网关）
     * @param name 分支名称
     * @param gatewayId 所属网关ID
     * @param branchIndex 分支索引
     * @param totalBranches 总分支数
     * @return 分支叶子节点
     */
    public static ConditionNodeCanvas createBranchLeafWithCondition(String name, String gatewayId, 
                                                                   int branchIndex, int totalBranches) {
        ConditionNodeCanvas branch = createBranchLeaf(name, gatewayId);
        setDefaultConditionForBranch(branch, branchIndex, totalBranches);
        
        log.debug("创建带条件的分支叶子节点: {} (索引: {}/{})", branch.getId(), branchIndex, totalBranches);
        return branch;
    }

    /**
     * 为分支设置默认条件
     * @param branch 分支节点
     * @param branchIndex 分支索引（从0开始）
     * @param totalBranches 总分支数
     */
    public static void setDefaultConditionForBranch(ConditionNodeCanvas branch, int branchIndex, int totalBranches) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");
            condition.setNodeName("系统");

            if (branchIndex == totalBranches - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (branchIndex + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (branchIndex + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 设置默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }

    /**
     * 创建标准的网关分支对（左右分支）
     * @param gatewayType 网关类型
     * @param gatewayId 网关ID
     * @return 分支对数组 [左分支, 右分支]
     */
    public static ConditionNodeCanvas[] createStandardBranchPair(Integer gatewayType, String gatewayId) {
        ConditionNodeCanvas leftBranch = createBranchLeaf("分支1", gatewayId);
        ConditionNodeCanvas rightBranch = createBranchLeaf("分支2", gatewayId);

        // 如果是唯一分支网关，设置默认条件
        if (GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE.equals(gatewayType)) {
            setDefaultConditionForBranch(leftBranch, 0, 2);
            setDefaultConditionForBranch(rightBranch, 1, 2);
        }

        log.debug("创建标准分支对: {} 和 {} (网关类型: {})", 
                leftBranch.getId(), rightBranch.getId(), gatewayType);

        return new ConditionNodeCanvas[]{leftBranch, rightBranch};
    }

    /**
     * 复制节点（深拷贝）
     * @param originalNode 原始节点
     * @param <T> 节点类型
     * @return 复制的节点
     */
    @SuppressWarnings("unchecked")
    public static <T extends BaseNodeCanvas> T copyNode(T originalNode) {
        if (originalNode == null) {
            return null;
        }

        try {
            // 这里应该实现深拷贝逻辑
            // 为了保持现有语义，暂时返回原节点的引用
            // 实际项目中应该实现真正的深拷贝
            log.warn("NodeFactory.copyNode 暂未实现深拷贝，返回原节点引用");
            return originalNode;
        } catch (Exception e) {
            log.error("复制节点失败: {}", originalNode.getId(), e);
            throw new RuntimeException("复制节点失败", e);
        }
    }

    /**
     * 验证节点创建参数
     * @param gatewayType 网关类型
     * @return 是否有效
     */
    public static boolean isValidGatewayType(Integer gatewayType) {
        return gatewayType != null && 
               (GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL.equals(gatewayType) ||
                GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE.equals(gatewayType));
    }
}
