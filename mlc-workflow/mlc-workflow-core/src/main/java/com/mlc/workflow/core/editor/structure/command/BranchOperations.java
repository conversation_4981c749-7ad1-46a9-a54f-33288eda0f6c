package com.mlc.workflow.core.editor.structure.command;

import com.mlc.base.common.utils.JacksonDeepCopyUtil;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.beans.ConditionGroup;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;

/**
 * 分支操作命令
 * 实现分支的新增、删除、排序、复制等操作
 */
@Slf4j
public class BranchOperations {

    private final AutoWireStrategy autoWireStrategy;
    private final GatewayOperations gatewayOperations;
    private final NodeBatchExecutor nodeBatchExecutor;
    
    public BranchOperations(NodeBatchExecutor nodeBatchExecutor,
        AutoWireStrategy autoWireStrategy, GatewayOperations gatewayOperations) {
        this.autoWireStrategy = autoWireStrategy;
        this.gatewayOperations = gatewayOperations;
        this.nodeBatchExecutor = nodeBatchExecutor;
    }
    
    /**
     * 新增分支
     * @param gatewayId 网关ID
     * @param position 插入位置（-1表示末尾）
     * @return 创建的分支叶子节点
     */
    public ConditionNodeCanvas addBranch(String gatewayId, int position) {
        if (nodeBatchExecutor == null || gatewayId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 创建新分支叶子
        ConditionNodeCanvas newBranch = new ConditionNodeCanvas();
        newBranch.setName("分支");
        newBranch.setPrveId(gatewayId);
        newBranch.setNextId(""); // 空分支，等待后续添加内容

        nodeBatchExecutor.createNode(newBranch);

        // 插入到指定位置
        List<String> flowIds = gateway.getFlowIds();
        if (position < 0 || position >= flowIds.size()) {
            flowIds.add(newBranch.getId());
        } else {
            flowIds.add(position, newBranch.getId());
        }

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 如果是唯一分支网关，为新分支生成默认条件（非else）
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            // 新分支总是生成非else条件，避免冲突
            generateNonElseConditionForBranch(newBranch, flowIds.size() - 1);
            nodeBatchExecutor.updateNode(newBranch.getId(), newBranch);
            log.debug("为新分支 {} 生成非else条件", newBranch.getId());
        }

        log.debug("为网关 {} 新增分支 {}，位置: {}", gatewayId, newBranch.getId(), position);

        return newBranch;
    }
    
    /**
     * 删除分支
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(String gatewayId, String branchLeafId) {
        if (nodeBatchExecutor == null || gatewayId == null || branchLeafId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> flowIds = gateway.getFlowIds();
        if (!flowIds.contains(branchLeafId)) {
            throw new IllegalArgumentException("分支叶子不属于该网关");
        }

        log.debug("开始删除网关 {} 的分支 {}，当前分支数: {}", gatewayId, branchLeafId, flowIds.size());

        // 检查要删除的分支叶子的连接状态
        BaseNodeCanvas branchToDelete = workingCopy.getFlowNodeMap().get(branchLeafId);
        if (branchToDelete instanceof IRoutable routableBranch) {
            log.debug("要删除的分支 {} 的nextId: {}, prveId: {}",
                     branchLeafId, routableBranch.getNextId(), routableBranch.getPrveId());
        }

        // 删除整条分支链
        deleteBranchChain(branchLeafId);

        // 从网关的flowIds中移除
        flowIds.remove(branchLeafId);

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        log.info("删除分支后，网关 {} 剩余分支数: {}", gatewayId, flowIds.size());

        // 检查剩余分支的状态
        for (String remainingBranchId : flowIds) {
            BaseNodeCanvas remainingBranch = workingCopy.getFlowNodeMap().get(remainingBranchId);
            if (remainingBranch instanceof IRoutable routableRemaining) {
                log.debug("剩余分支 {} 的nextId: {}, prveId: {}",
                         remainingBranchId, routableRemaining.getNextId(), routableRemaining.getPrveId());
            }
        }

        // 检查是否只剩一条分支
        if (flowIds.size() == 1) {
            // 在扁平化之前，修复剩余分支叶子的连接
            String remainingBranchId = flowIds.get(0);
            BaseNodeCanvas remainingBranch = workingCopy.getFlowNodeMap().get(remainingBranchId);
            log.info("准备扁平化网关 {}，剩余分支: {}", gatewayId, remainingBranchId);

            if (remainingBranch instanceof IRoutable routableBranch) {
                String branchNextId = routableBranch.getNextId();
                log.info("剩余分支 {} 的nextId: {}", remainingBranchId, branchNextId);

                if (branchNextId == null || branchNextId.trim().isEmpty()) {
                    // 分支叶子为空（等待合流），需要连接到网关的下游
                    String gatewayNextId = gateway.getNextId();
                    log.info("网关 {} 的nextId: {}", gatewayId, gatewayNextId);

                    if (EndOwnerManager.END_OWNER_ID.equals(gatewayNextId)) {
                        // 网关是EndOwner，剩余分支应该成为新的EndOwner
                        // 先清除网关的EndOwner状态，避免多个EndOwner
                        gateway.setNextId("");
                        nodeBatchExecutor.updateNode(gatewayId, gateway);
                        log.info("清除网关 {} 的EndOwner状态", gatewayId);

                        // 然后设置剩余分支为EndOwner
                        routableBranch.setNextId(EndOwnerManager.END_OWNER_ID);
                        log.info("剩余分支 {} 成为新的EndOwner", remainingBranchId);
                    } else {
                        // 网关不是EndOwner，剩余分支连接到网关的下游
                        routableBranch.setNextId(gatewayNextId);
                        log.info("剩余分支 {} 连接到网关下游: {}", remainingBranchId, gatewayNextId);
                    }
                    nodeBatchExecutor.updateNode(remainingBranchId, remainingBranch);
                } else {
                    log.info("剩余分支 {} 的nextId不为空，无需修复: {}", remainingBranchId, branchNextId);
                }
            }

            // 触发网关删除的扁平化逻辑
            gatewayOperations.deleteGateway(gatewayId);
        } else {
            // 如果是唯一分支网关，确保else的唯一性
            if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
                ensureElseUniqueness(workingCopy, gateway);
            }
        }

        // 最后确保所有剩余分支的nextId都是正确的
        ensureBranchNextIdConsistency(workingCopy, gatewayId);

        log.debug("删除网关 {} 的分支 {}", gatewayId, branchLeafId);
    }

    /**
     * 确保分支nextId的一致性
     * 修复可能被错误设置的分支nextId
     */
    private void ensureBranchNextIdConsistency(ProcessNode processNode, String gatewayId) {
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(processNode, gatewayId);
        if (gateway == null) {
            return;
        }

        List<String> flowIds = gateway.getFlowIds();
        if (flowIds == null) {
            return;
        }

        for (String branchId : flowIds) {
            BaseNodeCanvas branch = processNode.getFlowNodeMap().get(branchId);
            if (branch instanceof IRoutable routableBranch) {
                String branchNextId = routableBranch.getNextId();
                if (gatewayId.equals(branchNextId)) {
                    // 分支叶子不应该指向网关，修复为空字符串
                    routableBranch.setNextId("");
                    nodeBatchExecutor.updateNode(branchId, branch);
                    log.debug("修复分支 {} 的nextId，从网关 {} 改为空字符串", branchId, gatewayId);
                }
            }
        }
    }
    
    /**
     * 删除分支链
     * 根据设计方案：若分支链尾或中途节点曾是EndOwner，执行AbortEndOwnerIfFlatten
     */
    private void deleteBranchChain(String branchLeafId) {
        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        List<BaseNodeCanvas> branchChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);

        boolean hasEndOwner = false;

        // 先检查分支链中是否有EndOwner
        for (BaseNodeCanvas node : branchChain) {
            if (node instanceof IRoutable routableNode && EndOwnerManager.END_OWNER_ID.equals(routableNode.getNextId())) {
                hasEndOwner = true;
                log.debug("分支链中发现EndOwner节点: {}", node.getId());
                break;
            }
        }

        // 删除分支链中的所有节点
        for (BaseNodeCanvas node : branchChain) {
            nodeBatchExecutor.deleteNode(node.getId());
        }

        // 如果分支链中有EndOwner，需要触发修复
        if (hasEndOwner) {
            log.debug("分支链包含EndOwner，触发EndOwner修复");
            autoWireStrategy.abortEndOwnerIfFlatten(workingCopy);
        }
    }
    
    /**
     * 调整分支顺序
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(String gatewayId, List<String> newOrder) {
        if (nodeBatchExecutor == null || gatewayId == null || newOrder == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        List<String> currentFlowIds = gateway.getFlowIds();

        // 验证新顺序的有效性
        if (newOrder.size() != currentFlowIds.size() ||
            !new HashSet<>(newOrder).equals(new HashSet<>(currentFlowIds))) {
            throw new IllegalArgumentException("新顺序无效");
        }

        // 更新分支顺序
        gateway.setFlowIds(new ArrayList<>(newOrder));

        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 如果是唯一分支，同步更新优先序和else位置
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            updateExclusiveBranchPriority(workingCopy, gateway);
        }

        log.debug("调整网关 {} 的分支顺序", gatewayId);
    }
    
    /**
     * 复制分支
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置（-1表示末尾）
     * @return 复制的分支叶子节点
     */
    public ConditionNodeCanvas duplicateBranch(String gatewayId, String branchLeafId, int position) {
        if (nodeBatchExecutor == null || gatewayId == null || branchLeafId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        GatewayNodeCanvas gateway = WorkflowQueryService.findGateway(workingCopy, gatewayId);
        if (gateway == null) {
            throw new IllegalArgumentException("找不到网关节点: " + gatewayId);
        }

        // 获取原分支链
        List<BaseNodeCanvas> originalChain = WorkflowQueryService.findBranchChain(workingCopy, branchLeafId);
        if (originalChain.isEmpty()) {
            throw new IllegalArgumentException("找不到分支链");
        }

        // 深拷贝分支链
        Map<String, String> idMapping = new HashMap<>();
        List<BaseNodeCanvas> copiedChain = deepCopyBranchChain(originalChain, idMapping);

        for (BaseNodeCanvas copiedNode : copiedChain) {
            nodeBatchExecutor.createNode(copiedNode);
        }

        // 获取复制的分支叶子
        ConditionNodeCanvas copiedBranchLeaf = (ConditionNodeCanvas) copiedChain.get(0);
        copiedBranchLeaf.setPrveId(gatewayId);

        // 处理EndOwner问题
        handleEndOwnerInCopiedBranch(workingCopy, copiedChain, originalChain);

        // 插入到网关的flowIds
        List<String> flowIds = gateway.getFlowIds();
        if (position < 0 || position >= flowIds.size()) {
            flowIds.add(copiedBranchLeaf.getId());
        } else {
            flowIds.add(position, copiedBranchLeaf.getId());
        }

        // 更新网关节点
        nodeBatchExecutor.updateNode(gatewayId, gateway);

        // 如果是唯一分支，处理条件冲突
        if (gateway.getGatewayType() != null && gateway.getGatewayType() == GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE) {
            handleConditionConflicts(workingCopy, gateway, copiedBranchLeaf);
        }

        log.debug("复制网关 {} 的分支 {} 为 {}", gatewayId, branchLeafId, copiedBranchLeaf.getId());

        return copiedBranchLeaf;
    }
    
    /**
     * 深拷贝分支链
     */
    private List<BaseNodeCanvas> deepCopyBranchChain(List<BaseNodeCanvas> originalChain, Map<String, String> idMapping) {
        List<BaseNodeCanvas> copiedChain = new ArrayList<>();
        
        for (BaseNodeCanvas originalNode : originalChain) {
            BaseNodeCanvas copiedNode = JacksonDeepCopyUtil.deepCopy(originalNode);
            String newId = UUID.randomUUID().toString().replace("-", "");
            idMapping.put(originalNode.getId(), newId);
            copiedNode.setId(newId);
            copiedChain.add(copiedNode);
        }
        
        // 更新复制节点之间的连接关系
        for (int i = 0; i < copiedChain.size(); i++) {
            BaseNodeCanvas copiedNode = copiedChain.get(i);
            BaseNodeCanvas originalNode = originalChain.get(i);
            
            if (copiedNode instanceof IRoutable copiedRoutable && 
                originalNode instanceof IRoutable originalRoutable) {
                String originalNextId = originalRoutable.getNextId();
                if (originalNextId != null && idMapping.containsKey(originalNextId)) {
                    copiedRoutable.setNextId(idMapping.get(originalNextId));
                } else {
                    copiedRoutable.setNextId(originalNextId);
                }
            }
        }
        
        return copiedChain;
    }
    
    
    /**
     * 处理复制分支中的EndOwner问题
     * 根据设计方案：若原链尾是EndOwner，禁止让拷贝链尾也设99，而是令新尾nextId=原尾.id（扇入）
     */
    private void handleEndOwnerInCopiedBranch(ProcessNode processNode, List<BaseNodeCanvas> copiedChain,
                                            List<BaseNodeCanvas> originalChain) {
        for (int i = 0; i < copiedChain.size(); i++) {
            BaseNodeCanvas copiedNode = copiedChain.get(i);
            BaseNodeCanvas originalNode = originalChain.get(i);

            if (originalNode instanceof IRoutable originalRoutable &&
                EndOwnerManager.END_OWNER_ID.equals(originalRoutable.getNextId())) {
                // 原节点是EndOwner，根据设计方案：禁止让拷贝节点也设99
                if (copiedNode instanceof IRoutable copiedRoutable) {
                    // 使用AutoWireStrategy的connectToEnd，它会自动处理扇入
                    try {
                        autoWireStrategy.connectToEnd(processNode, copiedNode);
                        log.debug("复制的节点 {} 扇入到EndOwner", copiedNode.getId());
                    } catch (Exception e) {
                        log.error("处理复制分支的EndOwner时发生错误", e);
                        // 降级处理：直接扇入到原EndOwner
                        copiedRoutable.setNextId(originalNode.getId());
                        log.debug("降级处理：复制的节点 {} 直接扇入到原EndOwner {}",
                                 copiedNode.getId(), originalNode.getId());
                    }
                }
            }
        }
    }
    
    /**
     * 为分支生成默认条件
     */
    private void generateDefaultConditionForBranch(ConditionNodeCanvas branch, int index, int totalBranches) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            // 创建默认条件组
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");

            if (index == totalBranches - 1) {
                // 最后一个分支设为 else 条件
                condition.setFiledId("else");
                condition.setFiledValue("其他情况");
                condition.setConditionId("default_else");
                condition.setValue("else");
            } else {
                // 其他分支生成默认条件
                condition.setFiledId("condition_" + (index + 1));
                condition.setFiledValue("始终成立");
                condition.setConditionId("default_condition_" + (index + 1));
                condition.setValue("true");
            }

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成默认条件: {}", branch.getId(), condition.getFiledId());
        }
    }
    
    /**
     * 确保else条件的唯一性
     */
    private void ensureElseUniqueness(ProcessNode processNode, GatewayNodeCanvas gateway) {
        // 这里应该检查并确保只有一个else分支
        log.debug("确保网关 {} 的else条件唯一性", gateway.getId());
    }
    
    /**
     * 更新唯一分支的优先序
     */
    private void updateExclusiveBranchPriority(ProcessNode processNode, GatewayNodeCanvas gateway) {
        // 这里应该根据新顺序更新分支的优先级
        log.debug("更新网关 {} 的分支优先序", gateway.getId());
    }
    
    /**
     * 处理条件冲突
     */
    private void handleConditionConflicts(ProcessNode processNode, GatewayNodeCanvas gateway, ConditionNodeCanvas newBranch) {
        log.debug("处理网关 {} 新分支 {} 的条件冲突", gateway.getId(), newBranch.getId());

        // 为新分支生成非else条件（避免多个else分支）
        List<String> flowIds = gateway.getFlowIds();
        int branchIndex = flowIds.indexOf(newBranch.getId());
        if (branchIndex >= 0) {
            // 总是生成非else条件，避免冲突
            generateNonElseConditionForBranch(newBranch, branchIndex);
            log.debug("为新分支 {} 生成非else条件", newBranch.getId());
        } else {
            log.warn("无法找到新分支 {} 在网关 {} 中的位置", newBranch.getId(), gateway.getId());
        }
    }

    /**
     * 为分支生成非else条件
     */
    private void generateNonElseConditionForBranch(ConditionNodeCanvas branch, int index) {
        if (branch.getOperateCondition() == null || branch.getOperateCondition().isEmpty()) {
            List<List<ConditionGroup>> defaultConditions = new ArrayList<>();
            List<ConditionGroup> conditionGroup = new ArrayList<>();

            // 创建一个简单的条件组（非else）
            ConditionGroup condition = new ConditionGroup();
            condition.setNodeId("system");  // 设置必需的 nodeId
            condition.setNodeName("系统");
            condition.setFiledId("condition_" + (index + 1));
            condition.setFiledValue("始终成立");
            condition.setConditionId("default_condition_" + (index + 1));
            condition.setValue("true");

            conditionGroup.add(condition);
            defaultConditions.add(conditionGroup);
            branch.setOperateCondition(defaultConditions);

            log.debug("为分支 {} 生成非else条件: {}", branch.getId(), condition.getFiledId());
        }
    }
}
