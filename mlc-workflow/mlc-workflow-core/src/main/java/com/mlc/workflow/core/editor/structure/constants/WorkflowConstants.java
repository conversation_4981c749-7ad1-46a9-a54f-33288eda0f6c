package com.mlc.workflow.core.editor.structure.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * 工作流常量定义
 * 集中管理所有硬编码的常量，提高可维护性
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class WorkflowConstants {

    // ==================== EndOwner相关常量 ====================
    
    /**
     * EndOwner标识，表示流程结束
     */
    public static final String END_OWNER_ID = "99";
    
    /**
     * 分支结束标识，表示分支内部结束
     */
    public static final String BRANCH_END_ID = "";

    // ==================== 网关类型常量 ====================
    
    /**
     * 并行网关类型
     */
    public static final Integer GATEWAY_TYPE_PARALLEL = 1;
    
    /**
     * 唯一分支网关类型
     */
    public static final Integer GATEWAY_TYPE_EXCLUSIVE = 2;

    // ==================== 节点类型常量 ====================
    
    /**
     * 网关节点类型ID
     */
    public static final Integer NODE_TYPE_GATEWAY = 1;
    
    /**
     * 分支叶子节点类型ID
     */
    public static final Integer NODE_TYPE_CONDITION = 2;

    // ==================== 默认名称常量 ====================
    
    /**
     * 默认网关名称
     */
    public static final String DEFAULT_GATEWAY_NAME = "网关";
    
    /**
     * 默认并行网关名称
     */
    public static final String DEFAULT_PARALLEL_GATEWAY_NAME = "并行网关";
    
    /**
     * 默认唯一分支网关名称
     */
    public static final String DEFAULT_EXCLUSIVE_GATEWAY_NAME = "唯一分支网关";
    
    /**
     * 默认分支名称
     */
    public static final String DEFAULT_BRANCH_NAME = "分支";
    
    /**
     * 默认分支名称模板
     */
    public static final String DEFAULT_BRANCH_NAME_TEMPLATE = "分支%d";

    // ==================== 条件相关常量 ====================
    
    /**
     * 系统节点ID
     */
    public static final String SYSTEM_NODE_ID = "system";
    
    /**
     * 系统节点名称
     */
    public static final String SYSTEM_NODE_NAME = "系统";
    
    /**
     * else条件标识
     */
    public static final String ELSE_CONDITION_ID = "else";
    
    /**
     * else条件值
     */
    public static final String ELSE_CONDITION_VALUE = "其他情况";
    
    /**
     * 默认条件ID模板
     */
    public static final String DEFAULT_CONDITION_ID_TEMPLATE = "default_condition_%d";
    
    /**
     * 默认else条件ID
     */
    public static final String DEFAULT_ELSE_CONDITION_ID = "default_else";
    
    /**
     * 始终成立条件值
     */
    public static final String ALWAYS_TRUE_CONDITION_VALUE = "始终成立";
    
    /**
     * 真值条件
     */
    public static final String TRUE_VALUE = "true";

    // ==================== 遍历相关常量 ====================
    
    /**
     * 最大遍历深度，防止无限递归
     */
    public static final int MAX_TRAVERSE_DEPTH = 1000;
    
    /**
     * 默认上下文行数
     */
    public static final int DEFAULT_CONTEXT_LINES = 5;

    // ==================== 验证相关常量 ====================
    
    /**
     * 最小分支数量
     */
    public static final int MIN_BRANCH_COUNT = 1;
    
    /**
     * 建议的最小并行分支数量
     */
    public static final int RECOMMENDED_MIN_PARALLEL_BRANCHES = 2;
    
    /**
     * 最大分支数量（防止性能问题）
     */
    public static final int MAX_BRANCH_COUNT = 50;

    // ==================== 错误消息模板 ====================
    
    /**
     * 参数为空错误模板
     */
    public static final String ERROR_PARAM_NULL = "%s不能为空";
    
    /**
     * 节点不存在错误模板
     */
    public static final String ERROR_NODE_NOT_FOUND = "找不到%s节点: %s";
    
    /**
     * 节点类型错误模板
     */
    public static final String ERROR_NODE_TYPE_INVALID = "%s节点类型错误: %s";
    
    /**
     * 操作失败错误模板
     */
    public static final String ERROR_OPERATION_FAILED = "%s失败，节点: %s，原因: %s";
    
    /**
     * EndOwner唯一性错误
     */
    public static final String ERROR_MULTIPLE_END_OWNERS = "流程有多个EndOwner: %s，违反EndOwner唯一性约束";
    
    /**
     * 缺少EndOwner错误
     */
    public static final String ERROR_NO_END_OWNER = "流程缺少EndOwner（nextId=99的节点），违反EndOwner不变量";

    // ==================== 成功消息模板 ====================
    
    /**
     * 操作成功消息模板
     */
    public static final String SUCCESS_OPERATION = "%s成功，节点: %s";
    
    /**
     * 创建成功消息模板
     */
    public static final String SUCCESS_CREATE = "成功创建%s: %s";
    
    /**
     * 删除成功消息模板
     */
    public static final String SUCCESS_DELETE = "成功删除%s: %s";
    
    /**
     * 更新成功消息模板
     */
    public static final String SUCCESS_UPDATE = "成功更新%s: %s";

    // ==================== 日志消息模板 ====================
    
    /**
     * 开始操作日志模板
     */
    public static final String LOG_OPERATION_START = "开始%s，节点: %s";
    
    /**
     * 完成操作日志模板
     */
    public static final String LOG_OPERATION_COMPLETE = "完成%s，节点: %s";
    
    /**
     * 跳过操作日志模板
     */
    public static final String LOG_OPERATION_SKIP = "跳过%s，节点: %s，原因: %s";

    // ==================== 工具方法 ====================
    
    /**
     * 格式化错误消息
     * @param template 消息模板
     * @param args 参数
     * @return 格式化后的消息
     */
    public static String formatError(String template, Object... args) {
        return String.format(template, args);
    }
    
    /**
     * 格式化成功消息
     * @param template 消息模板
     * @param args 参数
     * @return 格式化后的消息
     */
    public static String formatSuccess(String template, Object... args) {
        return String.format(template, args);
    }
    
    /**
     * 格式化日志消息
     * @param template 消息模板
     * @param args 参数
     * @return 格式化后的消息
     */
    public static String formatLog(String template, Object... args) {
        return String.format(template, args);
    }
    
    /**
     * 生成默认分支名称
     * @param index 分支索引（从1开始）
     * @return 分支名称
     */
    public static String generateBranchName(int index) {
        return String.format(DEFAULT_BRANCH_NAME_TEMPLATE, index);
    }
    
    /**
     * 生成默认条件ID
     * @param index 条件索引（从1开始）
     * @return 条件ID
     */
    public static String generateConditionId(int index) {
        return String.format(DEFAULT_CONDITION_ID_TEMPLATE, index);
    }
    
    /**
     * 检查是否为EndOwner
     * @param nextId 下一个节点ID
     * @return 是否为EndOwner
     */
    public static boolean isEndOwner(String nextId) {
        return END_OWNER_ID.equals(nextId);
    }
    
    /**
     * 检查是否为分支结束
     * @param nextId 下一个节点ID
     * @return 是否为分支结束
     */
    public static boolean isBranchEnd(String nextId) {
        return nextId == null || BRANCH_END_ID.equals(nextId.trim());
    }
    
    /**
     * 检查是否为有效的网关类型
     * @param gatewayType 网关类型
     * @return 是否有效
     */
    public static boolean isValidGatewayType(Integer gatewayType) {
        return GATEWAY_TYPE_PARALLEL.equals(gatewayType) || GATEWAY_TYPE_EXCLUSIVE.equals(gatewayType);
    }
}
