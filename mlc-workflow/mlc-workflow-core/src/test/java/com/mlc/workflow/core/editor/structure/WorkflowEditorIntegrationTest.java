package com.mlc.workflow.core.editor.structure;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.HashMap;

import com.mlc.workflow.core.editor.model.canvas.ApprovalNode;
import com.mlc.workflow.core.editor.model.canvas.ApprovalProcessNode;
import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.ConditionNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.WriteNode;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.command.BranchOperations;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.GatewaySemanticsStrategy;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;


/**
 * 工作流编辑器集成测试
 * 验证设计方案中提到的关键边界场景
 */
@Slf4j
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
class WorkflowEditorIntegrationTest {
    
    private WorkflowEditor workflowEditor;
    
    private ProcessNode testProcessNode;
    
    @BeforeEach
    void setUp() {
        WorkflowValidator workflowValidator = new WorkflowValidator();
        NodeBatchExecutor nodeBatchExecutor = new NodeBatchExecutor(workflowValidator);
        AutoWireStrategy autoWireStrategy = new AutoWireStrategy(new EndOwnerManager());
        GatewayOperations gatewayOperations = new GatewayOperations(nodeBatchExecutor, autoWireStrategy);
        BranchOperations branchOperations = new BranchOperations(nodeBatchExecutor, autoWireStrategy, gatewayOperations);
        NodeOperations nodeOperations = new NodeOperations(nodeBatchExecutor, autoWireStrategy);

        workflowEditor = new WorkflowEditor(gatewayOperations, branchOperations, nodeOperations, nodeBatchExecutor, workflowValidator);
        testProcessNode = createBasicWorkflow();
        // 创建基础流程：开始 -> 审批 -> 结束
        testProcessNode = createBasicWorkflow();
    }
    
    /**
     * 创建基础工作流：开始 -> 审批 -> 结束
     */
    private ProcessNode createBasicWorkflow() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 开始事件
        StartEventNodeCanvas startEvent = new StartEventNodeCanvas();
        startEvent.setId("start-1");
        startEvent.setName("开始");
        startEvent.setNextId("approval-1");
        
        // 审批节点
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setId("approval-1");
        approvalNode.setName("审批");
        approvalNode.setPrveId("start-1");
        approvalNode.setNextId("99");
        
        // 添加到流程映射
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        processNode.getFlowNodeMap().put(approvalNode.getId(), approvalNode);
        processNode.setStartEventId(startEvent.getId());
        
        return processNode;
    }
    
    @Test
    void testScenario1_LeftPlacementWithEndOwner() {
        // 场景1：在某普通节点下方 LeftPlacement 新增并行网关，且下游已有一个直连结束（99）的节点
        // 验证 EndOwner 扇入策略
        
        // 在审批节点后添加网关（LeftPlacement）
        GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                              GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);
        
        assertNotNull(gateway);
        assertEquals(GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL, gateway.getGatewayType());
        
        // 验证网关有两个分支
        assertEquals(2, gateway.getFlowIds().size());
        
        // 验证EndOwner规则：只有一个节点的nextId=99
        List<BaseNodeCanvas> endOwners = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (BaseNodeCanvas) node)
                .filter(node -> "99".equals(((IRoutable) node).getNextId()))
                .toList();

        log.info("EndOwner节点列表: {}", endOwners.stream()
                .map(node -> node.getId() + "(" + node.getClass().getSimpleName() + ")")
                .toList());

        assertEquals(1, endOwners.size(), "应该只有一个EndOwner");
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario2_DuplicateBranchWithConditionConflict() {
        // 场景2：在唯一分支网关里复制分支，新分支条件与已有分支冲突
        
        // 先添加唯一分支网关
        GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                              GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 切换为唯一分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 复制分支
        String originalBranchId = gateway.getFlowIds().get(0);
        ConditionNodeCanvas duplicatedBranch = workflowEditor.duplicateBranch(testProcessNode,
                                                                              gateway.getId(),
                                                                              originalBranchId, -1);

        assertNotNull(duplicatedBranch);
        // 重新获取网关以获取最新的分支列表
        GatewayNodeCanvas updatedGateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
        assertEquals(3, updatedGateway.getFlowIds().size()); // 原来2个 + 复制的1个
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario3_DeleteBranchesToTriggerGatewayFlatten() {
        // 场景3：分支逐条删除直至只剩一条，触发"删除网关"扁平化逻辑
        
        // 添加网关
        GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                              GatewayOperations.PlacementStrategy.NO_MOVE);

        // 添加第三个分支
        workflowEditor.addBranch(testProcessNode, gateway.getId(), -1);

        // 重新获取网关以获取最新的分支列表
        GatewayNodeCanvas updatedGateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
        assertEquals(3, updatedGateway.getFlowIds().size());
        
        // 删除第一个分支
        String firstBranchId = updatedGateway.getFlowIds().get(0);
        workflowEditor.deleteBranch(testProcessNode, gateway.getId(), firstBranchId);

        // 重新获取网关以获取最新的分支列表
        updatedGateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());
        assertEquals(2, updatedGateway.getFlowIds().size());

        // 删除第二个分支，应该触发网关扁平化
        String secondBranchId = updatedGateway.getFlowIds().get(0);
        workflowEditor.deleteBranch(testProcessNode, gateway.getId(), secondBranchId);
        
        // 验证网关已被删除（扁平化）
        assertNull(testProcessNode.getFlowNodeMap().get(gateway.getId()));
        
        // 验证流程结构仍然有效
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "扁平化后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario4_SubProcessEndOwner() {
        // 场景4：在子流程内部新增节点直连结束，不影响主流程EndOwner
        
        // 创建包含子流程的节点
        ApprovalProcessNode subProcessNode = new ApprovalProcessNode();
        subProcessNode.setId("subprocess-1");
        subProcessNode.setName("审批子流程");
        
        // 创建子流程
        ProcessNode subProcess = createBasicWorkflow();
        subProcess.setId("sub-process");
        subProcessNode.setProcessNode(subProcess);
        
        // 插入子流程节点
        ApprovalProcessNode approvalProcessNode = new ApprovalProcessNode();
        approvalProcessNode.setId("subprocess-new");
        approvalProcessNode.setName("审批子流程");
        workflowEditor.insertNode(testProcessNode, "start-1", approvalProcessNode);
        
        // 在子流程中添加节点
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setId("sub-approval-new");
        approvalNode.setName("子流程审批");
        workflowEditor.insertNode(subProcess, "start-1", approvalNode);
        
        // 验证主流程和子流程都只有一个EndOwner
        long mainEndOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        long subEndOwnerCount = subProcess.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, mainEndOwnerCount, "主流程应该只有一个EndOwner");
        assertEquals(1, subEndOwnerCount, "子流程应该只有一个EndOwner");
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "包含子流程的流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario5_DeleteEndOwnerNode() {
        // 场景5：删除EndOwner节点，自动重选EndOwner并修复所有扇入

        // 简单测试：在EndOwner前插入一个节点，然后删除原EndOwner
        WriteNode nWriteNode = new WriteNode();
        nWriteNode.setId("write-new");
        nWriteNode.setName("填写");
        BaseNodeCanvas writeNode = workflowEditor.insertNode(testProcessNode, "start-1", nWriteNode);

        // 验证当前有一个EndOwner（应该是approval-1）
        BaseNodeCanvas currentEndOwner = null;
        for (BaseNodeCanvas node : testProcessNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routable && "99".equals(routable.getNextId())) {
                currentEndOwner = node;
                break;
            }
        }

        assertNotNull(currentEndOwner, "应该有一个EndOwner节点");
        assertEquals("approval-1", currentEndOwner.getId(), "EndOwner应该是approval-1");

        // 验证write-new指向approval-1
        if (writeNode instanceof IRoutable routableWrite) {
            assertEquals("approval-1", routableWrite.getNextId(), "write-new应该指向approval-1");
        }

        // 删除EndOwner节点（approval-1）
        workflowEditor.deleteNode(testProcessNode, "approval-1");

        // 打印删除后的状态
        System.out.println("=== 删除后的状态 ===");
        for (BaseNodeCanvas node : testProcessNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routable) {
                System.out.println("节点 " + node.getId() + " nextId: " + routable.getNextId() + ", prveId: " + routable.getPrveId());
            }
        }

        // 验证仍然只有一个EndOwner（应该是write-new）
        long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();

        assertEquals(1, endOwnerCount, "删除EndOwner后应该重新选择一个EndOwner");

        // 验证新的EndOwner是write-new
        BaseNodeCanvas newEndOwner = null;
        for (BaseNodeCanvas node : testProcessNode.getFlowNodeMap().values()) {
            if (node instanceof IRoutable routable && "99".equals(routable.getNextId())) {
                newEndOwner = node;
                break;
            }
        }

        assertNotNull(newEndOwner, "应该有一个新的EndOwner节点");
        assertEquals("write-new", newEndOwner.getId(), "新的EndOwner应该是write-new");

        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "删除EndOwner后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testScenario6_SwitchGatewayTypeWithBranchOperations() {
        // 场景6：改网关类型（并行↔唯一）后，继续对分支进行增删序拷，验证条件与尾部的健壮性
        
        // 添加并行网关
        GatewayNodeCanvas gateway = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                              GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 切换为唯一分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 添加分支
        workflowEditor.addBranch(testProcessNode, gateway.getId(), -1);

        // 重新获取网关以获取最新的分支列表
        GatewayNodeCanvas updatedGateway = (GatewayNodeCanvas) testProcessNode.getFlowNodeMap().get(gateway.getId());

        // 调整分支顺序
        List<String> newOrder = new ArrayList<>(updatedGateway.getFlowIds());
        Collections.reverse(newOrder);
        workflowEditor.reorderBranches(testProcessNode, gateway.getId(), newOrder);
        
        // 切换回并行分支
        workflowEditor.switchGatewayType(testProcessNode, gateway.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_PARALLEL);
        
        // 验证流程结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "网关类型切换后流程结构应该有效: " + result.getErrors());
    }
    
    @Test
    void testComplexWorkflowIntegration() {
        // 复杂集成测试：组合多种操作
        
        // 1. 添加网关
        GatewayNodeCanvas gateway1 = workflowEditor.addGateway(testProcessNode, "approval-1",
                                                               GatewayOperations.PlacementStrategy.LEFT_PLACEMENT);

        // 2. 在分支中添加节点
        String branchId = gateway1.getFlowIds().get(0);
        WriteNode nWriteNode = new WriteNode();
        nWriteNode.setId("write-form");
        nWriteNode.setName("填写表单");
        workflowEditor.insertNode(testProcessNode, branchId, nWriteNode);
        
        // 3. 添加嵌套网关
        GatewayNodeCanvas gateway2 = workflowEditor.addGateway(testProcessNode, branchId,
                                                               GatewayOperations.PlacementStrategy.NO_MOVE);
        
        // 4. 切换网关类型
        workflowEditor.switchGatewayType(testProcessNode, gateway2.getId(), 
                                       GatewaySemanticsStrategy.GATEWAY_TYPE_EXCLUSIVE);
        
        // 5. 复制分支
        String originalBranch = gateway2.getFlowIds().get(0);
        workflowEditor.duplicateBranch(testProcessNode, gateway2.getId(), originalBranch, -1);
        
        // 验证最终结构
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        assertTrue(result.isValid(), "复杂工作流结构应该有效: " + result.getErrors());
        
        // 验证EndOwner唯一性
        long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                .filter(node -> node instanceof IRoutable)
                .map(node -> (IRoutable) node)
                .filter(routable -> "99".equals(routable.getNextId()))
                .count();
        
        assertEquals(1, endOwnerCount, "复杂工作流应该只有一个EndOwner");
    }
}
