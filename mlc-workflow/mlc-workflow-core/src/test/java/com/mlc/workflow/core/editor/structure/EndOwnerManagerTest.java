package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.FlowNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.StartEventNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * EndOwnerManager 测试类
 * 验证 EndOwner 机制的核查和修复功能
 */
public class EndOwnerManagerTest {

    private EndOwnerManager endOwnerManager;
    private ProcessNode processNode;

    @BeforeEach
    void setUp() {
        endOwnerManager = new EndOwnerManager();
        processNode = createTestProcessNode();
    }

    @Test
    @DisplayName("测试 EndOwner 唯一性验证")
    void testValidateEndOwnerUnique() {
        // 测试没有 EndOwner 的情况
        assertFalse(endOwnerManager.validateEndOwnerUnique(processNode));

        // 添加一个 EndOwner
        FlowNode endOwnerNode = new FlowNode() {};
        endOwnerNode.setId("endOwner1");
        endOwnerNode.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner1", endOwnerNode);

        // 测试有一个 EndOwner 的情况
        assertTrue(endOwnerManager.validateEndOwnerUnique(processNode));

        // 添加第二个 EndOwner
        FlowNode endOwnerNode2 = new FlowNode() {};
        endOwnerNode2.setId("endOwner2");
        endOwnerNode2.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner2", endOwnerNode2);

        // 测试有多个 EndOwner 的情况
        assertFalse(endOwnerManager.validateEndOwnerUnique(processNode));
    }

    @Test
    @DisplayName("测试查找 EndOwner")
    void testFindEndOwner() {
        // 测试没有 EndOwner 的情况
        assertNull(endOwnerManager.findEndOwner(processNode));

        // 添加一个 EndOwner
        FlowNode endOwnerNode = new FlowNode() {};
        endOwnerNode.setId("endOwner1");
        endOwnerNode.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner1", endOwnerNode);

        // 测试找到 EndOwner
        BaseNodeCanvas foundEndOwner = endOwnerManager.findEndOwner(processNode);
        assertNotNull(foundEndOwner);
        assertEquals("endOwner1", foundEndOwner.getId());
    }

    @Test
    @DisplayName("测试统计 EndOwner 数量")
    void testCountEndOwners() {
        // 测试没有 EndOwner 的情况
        assertEquals(0, endOwnerManager.countEndOwners(processNode));

        // 添加一个 EndOwner
        FlowNode endOwnerNode = new FlowNode() {};
        endOwnerNode.setId("endOwner1");
        endOwnerNode.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner1", endOwnerNode);

        assertEquals(1, endOwnerManager.countEndOwners(processNode));

        // 添加第二个 EndOwner
        FlowNode endOwnerNode2 = new FlowNode() {};
        endOwnerNode2.setId("endOwner2");
        endOwnerNode2.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner2", endOwnerNode2);

        assertEquals(2, endOwnerManager.countEndOwners(processNode));
    }

    @Test
    @DisplayName("测试查找所有 EndOwner")
    void testFindAllEndOwners() {
        // 测试没有 EndOwner 的情况
        List<BaseNodeCanvas> endOwners = endOwnerManager.findAllEndOwners(processNode);
        assertTrue(endOwners.isEmpty());

        // 添加两个 EndOwner
        FlowNode endOwnerNode1 = new FlowNode() {};
        endOwnerNode1.setId("endOwner1");
        endOwnerNode1.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner1", endOwnerNode1);

        FlowNode endOwnerNode2 = new FlowNode() {};
        endOwnerNode2.setId("endOwner2");
        endOwnerNode2.setNextId("99");
        processNode.getFlowNodeMap().put("endOwner2", endOwnerNode2);

        endOwners = endOwnerManager.findAllEndOwners(processNode);
        assertEquals(2, endOwners.size());
    }

    @Test
    @DisplayName("测试设置节点为 EndOwner")
    void testSetAsEndOwner() {
        FlowNode node1 = new FlowNode() {};
        node1.setId("node1");
        processNode.getFlowNodeMap().put("node1", node1);

        // 测试设置第一个 EndOwner
        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.setAsEndOwner(processNode, node1);
        assertTrue(result.isSuccess());

        assertEquals("99", node1.getNextId());

        // 测试设置第二个 EndOwner（应该扇入）
        FlowNode node2 = new FlowNode() {};
        node2.setId("node2");
        processNode.getFlowNodeMap().put("node2", node2);

        result = endOwnerManager.setAsEndOwner(processNode, node2);
        assertTrue(result.isSuccess());
        assertEquals("node1", node2.getNextId()); // 扇入到第一个 EndOwner
    }

    @Test
    @DisplayName("测试连接节点到结束")
    void testConnectToEnd() {
        FlowNode node = new FlowNode() {};
        node.setId("node1");

        // 测试连接到结束
        EndOwnerManager.EndOwnerOperationResult result = endOwnerManager.connectToEnd(processNode, node);
        assertTrue(result.isSuccess());
        assertEquals("99", ((IRoutable) node).getNextId());
    }

    /**
     * 创建测试用的 ProcessNode
     */
    private ProcessNode createTestProcessNode() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("testProcess");
        processNode.setStartEventId("start");
        processNode.setFlowNodeMap(new HashMap<>());

        // 添加开始节点
        StartEventNodeCanvas startNode = new StartEventNodeCanvas();
        startNode.setId("start");
        processNode.getFlowNodeMap().put("start", startNode);

        return processNode;
    }

    /**
     * 创建测试用的 FlowNode 抽象类实现
     */
    private static abstract class FlowNode extends FlowNodeCanvas {
        // 空实现，用于测试
    }
}
