package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.model.BaseNodeCanvas;
import com.mlc.workflow.core.editor.model.canvas.GatewayNodeCanvas;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.model.canvas.capability.IRoutable;
import com.mlc.workflow.core.editor.runtime.utils.ProcessParser;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.command.BranchOperations;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.manager.EndOwnerManager;
import com.mlc.workflow.core.editor.structure.executor.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import java.nio.file.Files;
import java.nio.file.Paths;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 基于真实工作流数据的测试
 * 使用 workFlow.txt 中的复杂流程进行测试
 */
@Slf4j
@SpringBootTest
@ContextConfiguration(classes = WorkflowEditorTestConfig.class)
public class RealWorkflowTest {

    private WorkflowEditor workflowEditor;

    private WorkflowValidator workflowValidator;

    private ProcessNode realWorkflow;

    @BeforeEach
    void setUp() throws Exception {
        workflowValidator = new WorkflowValidator();
        NodeBatchExecutor nodeBatchExecutor = new NodeBatchExecutor(workflowValidator);
        AutoWireStrategy autoWireStrategy = new AutoWireStrategy(new EndOwnerManager());
        GatewayOperations gatewayOperations = new GatewayOperations(nodeBatchExecutor, autoWireStrategy);
        BranchOperations branchOperations = new BranchOperations(nodeBatchExecutor, autoWireStrategy, gatewayOperations);
        NodeOperations nodeOperations = new NodeOperations(nodeBatchExecutor, autoWireStrategy);

        workflowEditor = new WorkflowEditor(gatewayOperations, branchOperations, nodeOperations, nodeBatchExecutor, workflowValidator);

        realWorkflow = loadRealWorkflow();
        
        log.info("加载真实工作流，节点数量: {}", realWorkflow.getFlowNodeMap().size());
        log.info("开始事件ID: {}", realWorkflow.getStartEventId());
    }

    /**
     * 从 workFlow.txt 加载真实的工作流数据
     */
    private ProcessNode loadRealWorkflow() throws Exception {
        String filePath = "src/test/resources/workFlow.txt";
        String workflowJson = Files.readString(Paths.get(filePath));

        log.info("开始解析工作流JSON...");
        return new ProcessParser().parseProcessNode(workflowJson);
    }


    @Test
    void testRealWorkflowStructure() {
        // 验证工作流结构的完整性
        WorkflowValidator.ValidationResult result = workflowValidator.validate(realWorkflow);
        
        log.info("验证结果: {}", result.isValid() ? "通过" : "失败");
        if (!result.isValid()) {
            log.error("验证错误: {}", result.getErrors());
        }
        
        // 打印工作流结构信息
        log.info("=== 工作流结构分析 ===");
        log.info("流程ID: {}", realWorkflow.getId());
        log.info("开始事件: {}", realWorkflow.getStartEventId());
        log.info("节点总数: {}", realWorkflow.getFlowNodeMap().size());
        
        // 统计各类型节点数量
        Map<String, Integer> nodeTypeCount = new HashMap<>();
        for (BaseNodeCanvas node : realWorkflow.getFlowNodeMap().values()) {
            String typeName = node.getClass().getSimpleName();
            nodeTypeCount.put(typeName, nodeTypeCount.getOrDefault(typeName, 0) + 1);
        }
        
        log.info("节点类型统计: {}", nodeTypeCount);
        
        // 基本断言
        assertNotNull(realWorkflow.getId());
        assertNotNull(realWorkflow.getStartEventId());
        assertFalse(realWorkflow.getFlowNodeMap().isEmpty());
        assertTrue(realWorkflow.getFlowNodeMap().containsKey(realWorkflow.getStartEventId()));
    }

    @Test
    void testAddGatewayToRealWorkflow() {
        // 在真实工作流中添加网关
        // 选择一个合适的位置插入网关

        // 找到主流程中的审批流程节点 "65e986ef7cd0e536c5e0367f"
        BaseNodeCanvas targetNode = realWorkflow.getFlowNodeMap().get("65e9874f57c8d64b906944b4");

        log.info("目标节点: {}, 类型: {}",
            targetNode != null ? targetNode.getId() : "null",
            targetNode != null ? targetNode.getClass().getSimpleName() : "null");

        if (targetNode instanceof IRoutable) {
            log.info("在节点 {} 后添加网关", targetNode.getId());

            try {
                // 添加网关
                GatewayNodeCanvas gateway = workflowEditor.addGateway(realWorkflow, targetNode.getId(),
                                                                      GatewayOperations.PlacementStrategy.NO_MOVE);

                String json = realWorkflow.toJson();
                assertNotNull(gateway);
                log.info("成功添加网关: {}", gateway.getId());

                // 验证结果
                WorkflowValidator.ValidationResult result = workflowValidator.validate(realWorkflow);
                if (!result.isValid()) {
                    log.error("添加网关后验证失败: {}", result.getErrors());
                }

            } catch (Exception e) {
                log.error("添加网关失败", e);
                fail("添加网关操作失败: " + e.getMessage());
            }
        } else {
            // 如果目标节点不可路由，尝试其他节点
            log.warn("目标节点不可路由，尝试其他节点");

            // 列出所有可路由的节点
            for (Map.Entry<String, BaseNodeCanvas> entry : realWorkflow.getFlowNodeMap().entrySet()) {
                BaseNodeCanvas node = entry.getValue();
                if (node instanceof IRoutable) {
                    log.info("可路由节点: {} ({})", node.getId(), node.getClass().getSimpleName());
                }
            }
        }
    }

    @Test
    void testComplexWorkflowOperations() {
        // 在真实工作流上执行复杂操作序列
        log.info("开始复杂工作流操作测试");
        
        // 1. 验证初始状态
        WorkflowValidator.ValidationResult initialResult = workflowValidator.validate(realWorkflow);
        log.info("初始验证结果: {}", initialResult.isValid() ? "通过" : "失败");
        
        // 2. 找到合适的操作点
        // 在主流程的审批节点后添加网关
        BaseNodeCanvas approvalProcess = realWorkflow.getFlowNodeMap().get("65e986ef7cd0e536c5e0367f");
        
        if (approvalProcess != null) {
            try {
                log.info("在审批流程节点 {} 后执行操作", approvalProcess.getId());
                
                // 这里可以添加更多复杂的操作测试
                // 比如：添加网关、添加分支、插入节点等
                
                assertTrue(true, "复杂操作测试完成");
                
            } catch (Exception e) {
                log.error("复杂操作失败", e);
                fail("复杂操作测试失败: " + e.getMessage());
            }
        }
    }
}
